export enum IndicadorDashboard {
	TOTAL_ALUNOS,
	ATIVOS,
	INATIVOS,
	ATIVOS_COM_TREINO,
	COM_AVALIACAO_FISICA,
	SEM_AVALIACAO,
	ATIVOS_SEM_TREINO,
	EM_DIA,
	VENCIDOS,
	TREINOS_A_VENCER,
	AVALIACOES,
	ESTRELAS_5,
	ESTRELAS_4,
	ESTRELAS_3,
	ESTRELAS_2,
	ESTRELAS_1,
	R<PERSON><PERSON><PERSON>RA<PERSON>,
	NAO_RENOVARAM,
	ALUNOS_A_VENCER,
	TROCARAM_CARTEIRA,
	NOVOS_CARTEIRA,
	BI_TEMPO_CARTEIRA,
	BI_TEMPO_PROGRAMA,
	ACESSOS,
	ACESSOS_TREINO,
	EXECUCOES_TREINO,
	SMARTPHONE,
	AGENDAMENTOS_DISPONIBILIDADE,
	HRS_DISPONIBILIDADE,
	HRS_ATENDIMENTO,
	OCUPACAO,
	NOVOS_TREINOS,
	TREINOS_RENOVADOS,
	TREINOS_REVISADOS,
	AVALIACOES_FISICAS,
	FALTARAM,
	REAGENDARAM,
	COMPARECERAM,
	CANCELARAM,
	AG_CONFIRMACAO,
	CONFIRMARAM,
	AGENDAMENTOS,
	NOVOS_CARTEIRA_NOVOS,
	NOVOS_CARTEIRA_TROCARAM,
	PERCENTUAL_CRESCIMENTO_CARTEIRA,
	PERCENTUAL_RENOVACAO_CARTEIRA,
	PERC_TREINO_VENCIDOS,
	PERC_TREINO_EM_DIA,
	ALUNOS_APP_INSTALADO,
	ALUNOS_APP_NAO_INSTALADO,
	ALUNOS_APP_INSTALADO_ATIVOS,
	ALUNOS_CANCELADOS,
	ACOMPANHADOS,
	ATIVIDADES_ACOMPANHADAS,
}

export class AgrupamentoConfiguracaoRankingModel {
	AGENDA: Array<ConfiguracaoRankingModel>;
	TREINO: Array<ConfiguracaoRankingModel>;
	ALUNOS: Array<ConfiguracaoRankingModel>;
}

export interface ConfiguracaoRankingModel {
	id?: number;
	indicador: IndicadorDashboard;
	operacao: boolean;
	peso: number;
}

export interface RankingAluno {
	posicao?: number;
	nome?: string;
	visitante?: boolean;
	foto?: string;
	campos?: string;
	tempo?: number;
	peso?: number;
	repeticoes?: number;
	rounds?: number;
	nivelSigla?: RankingNivelSigla;
	sexo?: string; // 'M' ou 'F'
}

export enum RankingNivelSigla {
	IN = "IN",
	SC = "SC",
	AM = "AM",
	RX = "RX",
}
