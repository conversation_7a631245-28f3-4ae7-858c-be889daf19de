import { Injectable } from "@angular/core";
import { MenuConfigService } from "../../menu-config.service";
import { LayoutNavigationService } from "../../../../layout-navigation.service";
import { Observable, of } from "rxjs";
import { PlatformMenuItem } from "../../../../models";
import { PermissaoService } from "../../../../permissao/permissao.service";

@Injectable({
	providedIn: "root",
})
export class FinRelatoriosService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		private permissaoService: PermissaoService
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([this.relatorioParent]);
	}

	get relatorioParent(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "relatorios",
			configMenuSidebar: {
				submenus: [
					this.relatorioCaixaAdministrativoMenuItem,
					this.relatorioRelatorioChequesDevolvidosMenuItem,
					this.relatorioChequeConsultarCaixaMenuItem,
					this.relatorioContasPagarMenuItem,
					this.relatorioContarReceberMenuItem,
					this.relatorioDREFInanceiroMenuItem,
					this.relatorioDemonstrativoFinanceiroMenuItem,
					this.relatorioFluxoCaixaMenuItem,
					this.relatorioMovimentacaoFinanceiraMenuItem,
					this.relatorioOrcamentarioMenuItem,
					this.relatorioResumoContasMenuItem,
					this.relatorioVerLancamentosMenuItem,
				],
			},
			configMenuExplorar: {
				submenus: [
					this.relatorioCaixaAdministrativoMenuItem,
					this.relatorioRelatorioChequesDevolvidosMenuItem,
					this.relatorioChequeConsultarCaixaMenuItem,
					this.relatorioContasPagarMenuItem,
					this.relatorioContarReceberMenuItem,
					this.relatorioDREFInanceiroMenuItem,
					this.relatorioDemonstrativoFinanceiroMenuItem,
					this.relatorioFluxoCaixaMenuItem,
					this.relatorioMovimentacaoFinanceiraMenuItem,
					this.relatorioOrcamentarioMenuItem,
					this.relatorioResumoContasMenuItem,
					this.relatorioVerLancamentosMenuItem,
				],
			},
		};
		this.setParentIdMenuSidebar(menu);
		this.setParentIdMenuExplorar(menu);
		return menu;
	}

	get relatorioCaixaAdministrativoMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioCaixaAdministrativo",
			permissaoAdm:
				"Você precisa ter configurado no financeiro a utilização de movimentação de contas e ter a permissão 9.17 - Abrir Caixa-Administrativo",
			permitido: this.permissaoService.temPermissaoAdm("9.17"),
			favoriteIdentifier: "CAIXA_ADIMISTRATIVO",
			route: {
				queryParams: {
					funcionalidadeNome: "CAIXA_ADIMISTRATIVO",
					jspPage: "pages/finan/telaCaixa.jsp",
				},
			},
		};
	}

	get relatorioChequeConsultarCaixaMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioChequeConsultarCaixa",
			permissaoAdm:
				"Você precisa ter configurado no financeiro a utilização de movimentação de contas e uma das permissões: 9.17 - Abrir Caixa-Administrativo ou 9.18 - Consultar Histórico Caixa-Administrativo",
			permitido:
				this.permissaoService.temPermissaoAdm("9.17") ||
				this.permissaoService.temPermissaoAdm("9.18"),
			favoriteIdentifier: "CONSULTAR_CAIXA",
			route: {
				queryParams: {
					funcionalidadeNome: "CONSULTAR_CAIXA",
				},
			},
		};
	}

	get relatorioContasPagarMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioContasPagar",
			permissaoAdm: "9.29 - Lançar Contas a Pagar - Autorizar",
			permitido: this.permissaoService.temPermissaoAdm("9.29"),
			favoriteIdentifier: "CONTAS_A_PAGAR",
			route: {
				queryParams: {
					funcionalidadeNome: "CONTAS_A_PAGAR",
					jspPage: "pages/finan/telaLancamentosCons.jsp",
				},
			},
		};
	}

	get relatorioContarReceberMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioContarReceber",
			permissaoAdm: "9.29 - Lançar Contas a Pagar - Autorizar",
			permitido: this.permissaoService.temPermissaoAdm("9.29"),
			favoriteIdentifier: "CONTAS_A_RECEBER",
			route: {
				queryParams: {
					funcionalidadeNome: "CONTAS_A_RECEBER",
					jspPage: "pages/finan/telaLancamentosCons.jsp",
				},
			},
		};
	}

	get relatorioDemonstrativoFinanceiroMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioDemonstrativoFinanceiro",
			permissaoAdm: "9.27 - BI Financeiro",
			permitido: this.permissaoService.temPermissaoAdm("9.27"),
			favoriteIdentifier: "DEMONSTRATIVO_FINAN",
			route: {
				queryParams: {
					funcionalidadeNome: "DEMONSTRATIVO_FINAN",
					jspPage: "pages/finan/demonstrativoFinanceiro.jsp",
				},
			},
		};
	}

	get relatorioDREFInanceiroMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioDREFinanceiro",
			permissaoAdm: "9.27 - BI Financeiro",
			permitido: this.permissaoService.temPermissaoAdm("9.27"),
			favoriteIdentifier: "DRE",
			route: {
				queryParams: {
					funcionalidadeNome: "DRE",
					jspPage: "pages/finan/dre.jsp",
				},
			},
		};
	}

	get relatorioFluxoCaixaMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioFluxoCaixa",
			permissaoAdm: "9.58 - Permitir consultar fluxo de caixa",
			permitido: this.permissaoService.temPermissaoAdm("9.58"),
			favoriteIdentifier: "FLUXO_CAIXA_FINAN",
			route: {
				queryParams: {
					funcionalidadeNome: "FLUXO_CAIXA_FINAN",
					jspPage: "pages/finan/fluxoCaixa.jsp",
				},
			},
		};
	}

	get relatorioMovimentacaoFinanceiraMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioMovimentacaoFinanceira",
			permissaoAdm:
				"Você precisa ter configurado no financeiro a utilização de movimentação de contas e ter a permissão 9.27 - BI Financeiro",
			permitido: this.permissaoService.temPermissaoAdm("9.27"),
			// TODO: this.permissaoService.temConfiguracaoFinanceiro('usarMovimentacaoContas')
			favoriteIdentifier: "MOVIMENTACOES_FINAN",
			route: {
				queryParams: {
					funcionalidadeNome: "MOVIMENTACOES_FINAN",
					openAsPopup: true,
					popupFullscreen: true,
					windowTitle: "Movimentações financeiras",
				},
			},
		};
	}

	get relatorioOrcamentarioMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioOrcamentario",
			permissaoAdm: "9.27 - BI Financeiro",
			permitido: this.permissaoService.temPermissaoAdm("9.27"),
			favoriteIdentifier: "RELATORIO_ORCAMENTARIO",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_ORCAMENTARIO",
					openAsPopup: true,
					popupFullscreen: true,
					windowTitle: "Movimentações financeiras",
				},
			},
		};
	}

	get relatorioRelatorioChequesDevolvidosMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioRelatorioChequesDevolvidos",
			permissaoAdm:
				"Você precisa ter configurado no financeiro a utilização de movimentação de contas e ter a permissão 9.27 - BI Financeiro",
			permitido: this.permissaoService.temPermissaoAdm("9.27"),
			// TODO: this.permissaoService.temConfiguracaoFinanceiro('usarMovimentacaoContas')
			favoriteIdentifier: "RELATORIO_DEVOLUCAO_CHEQUE",
			route: {
				queryParams: {
					funcionalidadeNome: "RELATORIO_DEVOLUCAO_CHEQUE",
					openAsPopup: true,
					windowTitle: "Relatório de cheques devolvidos",
					windowHeight: 595,
					windowWidth: 880,
				},
			},
		};
	}

	get relatorioResumoContasMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioResumoContas",
			permissaoAdm:
				"Você precisa ter configurado no financeiro a utilização de movimentação de contas e ter a permissão 9.26 - Resumo de contas",
			permitido:
				this.permissaoService.temPermissaoAdm("9.26") &&
				this.permissaoService.temConfiguracaoFinanceiro(
					"usarmovimentacaocontas"
				),
			favoriteIdentifier: "RESUMO_CONTAS",
			route: {
				queryParams: {
					funcionalidadeNome: "RESUMO_CONTAS",
					jspPage: "pages/finan/resumoContas.jsp",
				},
			},
		};
	}

	get relatorioVerLancamentosMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioVerLancamentos",
			permissaoAdm: "9.32 - Visualizar lançamentos",
			permitido: this.permissaoService.temPermissaoAdm("9.32"),
			favoriteIdentifier: "LANCAMENTOS",
			route: {
				queryParams: {
					funcionalidadeNome: "LANCAMENTOS",
					jspPage: "pages/finan/telaLancamentosCons.jsp",
				},
			},
		};
	}
}
