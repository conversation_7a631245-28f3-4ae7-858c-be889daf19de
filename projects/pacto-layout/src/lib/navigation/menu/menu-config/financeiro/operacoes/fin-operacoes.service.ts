import { Injectable } from "@angular/core";
import { MenuConfigService } from "../../menu-config.service";
import { LayoutNavigationService } from "../../../../layout-navigation.service";
import { Observable, of } from "rxjs";
import { PlatformMenuItem } from "../../../../models";
import { PermissaoService } from "../../../../permissao/permissao.service";

@Injectable({
	providedIn: "root",
})
export class FinOperacoesService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		private permissaoService: PermissaoService
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([
			this.abrirCaixaItem,
			this.bloqueioCaixaItem,
			this.fecharCaixaItem,
			this.lotesMenuItem,
			this.lancamentoContaRapidoItem,
			this.novaContaPagarItem,
			this.novaContaReceberItem,
			this.recebiveisMenuItem,
		]);
	}

	get operacoesParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "operacoes",
			configMenuSidebar: {
				submenus: [
					this.operacoesCaixaParentItem,
					this.operacoesContaParentItem,
					this.operacoesRecebiveisParentItem,
				],
			},
			configMenuExplorar: {
				submenus: [
					this.operacoesCaixaParentItem,
					this.operacoesContaParentItem,
					this.operacoesRecebiveisParentItem,
				],
			},
		};
		this.setParentIdMenuSidebar(menu);
		this.setParentIdMenuExplorar(menu);

		return menu;
	}

	get lotesMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioLotes",
			permissaoAdm: "9.02 - Gestão de Lotes",
			permitido: this.permissaoService.temPermissaoAdm("9.02"),
			favoriteIdentifier: "LOTES",
			route: {
				queryParams: {
					funcionalidadeNome: "LOTES",
					jspPage: "pages/finan/gestaoLotesCons.jsp",
				},
			},
		};
	}

	get operacoesCaixaParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "operacoes-caixa",
			configMenuSidebar: {
				submenus: [
					this.abrirCaixaItem,
					this.bloqueioCaixaItem,
					this.fecharCaixaItem,
					this.lotesMenuItem,
				],
			},
			configMenuExplorar: {
				submenus: [
					this.abrirCaixaItem,
					this.bloqueioCaixaItem,
					this.fecharCaixaItem,
					this.lotesMenuItem,
				],
			},
		};
		this.setParentIdMenuSidebar(menu);
		this.setParentIdMenuExplorar(menu);

		return menu;
	}

	get operacoesRecebiveisParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "operacoes-recebiveis",
			configMenuSidebar: {
				submenus: [this.recebiveisMenuItem],
			},
			configMenuExplorar: {
				submenus: [this.recebiveisMenuItem],
			},
		};
		this.setParentIdMenuSidebar(menu);
		this.setParentIdMenuExplorar(menu);

		return menu;
	}

	get operacoesContaParentItem(): PlatformMenuItem {
		const menu: PlatformMenuItem = {
			id: "operacoes-conta",
			configMenuSidebar: {
				submenus: [
					this.contaMenuItem,
					this.lancamentoContaRapidoItem,
					this.novaContaPagarItem,
					this.novaContaReceberItem,
				],
			},
			configMenuExplorar: {
				submenus: [
					this.contaMenuItem,
					this.lancamentoContaRapidoItem,
					this.novaContaPagarItem,
					this.novaContaReceberItem,
				],
			},
		};
		this.setParentIdMenuSidebar(menu);
		this.setParentIdMenuExplorar(menu);

		return menu;
	}

	get abrirCaixaItem(): PlatformMenuItem {
		return {
			id: "abrir-caixa",
			permissaoAdm:
				"Você precisa ter configurado no financeiro a utilização de movimentação de contas, não ter um caixa em aberto e ter a permisão 9.17 - Abrir Caixa-Administrativo",
			permitido:
				this.permissaoService.temPermissaoAdm("9.17") &&
				!this.permissaoService.temCaixaEmAberto() &&
				this.permissaoService.temConfiguracaoFinanceiro(
					"usarmovimentacaocontas"
				),
			favoriteIdentifier: "ABRIR_CAIXA",
			route: {
				queryParams: {
					funcionalidadeNome: "ABRIR_CAIXA",
				},
			},
		};
	}

	get recebiveisMenuItem(): PlatformMenuItem {
		return {
			id: "relatorioRecebiveis",
			permissaoAdm: "9.01 - Gestão de Recebíveis",
			permitido: this.permissaoService.temPermissaoAdm("9.01"),
			favoriteIdentifier: "RECEBIVEIS",
			route: {
				queryParams: {
					funcionalidadeNome: "RECEBIVEIS",
					jspPage: "pages/finan/gestaoRecebiveis.jsp",
				},
			},
		};
	}

	get bloqueioCaixaItem(): PlatformMenuItem {
		return {
			id: "bloqueio-caixa",
			permissaoAdm:
				"Você precisa ter configurado no financeiro a utilização de movimentação de contas, não ter um caixa em aberto e ter a permisão 9.35 - Adicionar data de bloqueio de caixa",
			permitido:
				this.permissaoService.temPermissaoAdm("9.35") &&
				this.permissaoService.temConfiguracaoFinanceiro(
					"usarmovimentacaocontas"
				),
			favoriteIdentifier: "BLOQUEIO_CAIXA",
			route: {
				queryParams: {
					funcionalidadeNome: "BLOQUEIO_CAIXA",
					jspPage: "pages/finan/bloqueiocaixa.jsp",
				},
			},
		};
	}

	get fecharCaixaItem(): PlatformMenuItem {
		return {
			id: "fechar-caixa",
			permissaoAdm:
				"Você precisa ter configurado no financeiro a utilização de movimentação de contas, não ter um caixa em aberto e ter a permisão 9.17 - Abrir Caixa-Administrativo",
			permitido:
				this.permissaoService.temPermissaoAdm("9.17") &&
				this.permissaoService.temCaixaEmAberto() &&
				this.permissaoService.temConfiguracaoFinanceiro(
					"usarmovimentacaocontas"
				),
			favoriteIdentifier: "FECHAR_CAIXA",
			route: {
				queryParams: {
					funcionalidadeNome: "FECHAR_CAIXA",
					openAsPopup: true,
					windowTitle: "Conta",
					windowHeight: 595,
					windowWidth: 780,
				},
			},
		};
	}

	get lancamentoContaRapidoItem(): PlatformMenuItem {
		return {
			id: "lancamento-conta-rapido",
			permissaoAdm: "9.32 - Visualizar lançamentos",
			permitido: this.permissaoService.temPermissaoAdm("9.32"),
			favoriteIdentifier: "LANCAMENTO_CONTA_RAPIDO",
			route: {
				queryParams: {
					funcionalidadeNome: "LANCAMENTO_CONTA_RAPIDO",
					jspPage: "pages/finan/lancamentoFinanceiroRapidoForm.jsp",
				},
			},
		};
	}

	get novaContaPagarItem(): PlatformMenuItem {
		return {
			id: "nova-conta-pagar",
			permissaoAdm: "9.29 - Lançar Contas a Pagar - Autorizar",
			permitido: this.permissaoService.temPermissaoAdm("9.29"),
			favoriteIdentifier: "NOVA_CONTA_PAGAR",
			route: {
				queryParams: {
					funcionalidadeNome: "NOVA_CONTA_PAGAR",
					jspPage: "pages/finan/telaLancamentosForm.jsp",
				},
			},
		};
	}

	get novaContaReceberItem(): PlatformMenuItem {
		return {
			id: "nova-conta-receber",
			permissaoAdm: "9.29 - Lançar Contas a Pagar - Autorizar",
			permitido: this.permissaoService.temPermissaoAdm("9.29"),
			favoriteIdentifier: "NOVA_CONTAS_RECEBER",
			route: {
				queryParams: {
					funcionalidadeNome: "NOVA_CONTAS_RECEBER",
					jspPage: "pages/finan/telaLancamentosForm.jsp",
				},
			},
		};
	}

	get contaMenuItem(): PlatformMenuItem {
		return {
			id: "cadastroConta",
			// TODO: migrar para permissaoDescricao
			permissaoAdm:
				"Você precisa ter configurado no financeiro a utilização de movimentação de contas e ter a permissão 9.06 - Contas",
			permitido:
				this.permissaoService.temPermissaoAdm("9.06") &&
				this.permissaoService.temConfiguracaoFinanceiro(
					"usarmovimentacaocontas"
				),
			favoriteIdentifier: "FINAN_CONTA",
			route: {
				queryParams: {
					funcionalidadeNome: "FINAN_CONTA",
					openAsPopup: true,
					windowTitle: "Conta",
					windowHeight: 595,
					windowWidth: 780,
				},
			},
		};
	}
}
