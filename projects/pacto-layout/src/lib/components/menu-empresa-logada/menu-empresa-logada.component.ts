import {
	ChangeDetectorR<PERSON>,
	Component,
	OnDestroy,
	OnInit,
	Optional,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import {
	LoginUrlQueries,
	PactoLayoutSDKWrapper,
	PlataformaMenuV2Config,
} from "../../sdk-wrapper/sdk-wrappers";
import { Subscription } from "rxjs";
import { FormControl } from "@angular/forms";
import { LoginAppApiLoginService, TrocaEmpresa } from "login-app-api";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { LayoutNavigationService } from "../../navigation/layout-navigation.service";

@Component({
	selector: "pacto-menu-empresa-logada",
	templateUrl: "./menu-empresa-logada.component.html",
	styleUrls: ["./menu-empresa-logada.component.scss"],
})
export class MenuEmpresaLogadaComponent implements On<PERSON>nit, On<PERSON><PERSON>roy {
	@ViewChild("mudarEmpresa", { static: true })
	public mudarEmpresa: TemplateRef<any>;
	configuracao: PlataformaMenuV2Config;
	subscription: Subscription;
	currentCompany = {
		nome: "Nome fantasia da academia",
	};
	public fc: FormControl = new FormControl();
	public listaTrocaEmpresas: TrocaEmpresa[] = [];
	public usuarioGeral: string;

	constructor(
		@Optional() private pactoLayoutSDKWrapper: PactoLayoutSDKWrapper,
		private cd: ChangeDetectorRef,
		private readonly loginAppApiLogin: LoginAppApiLoginService,
		private readonly modal: NgbModal,
		private readonly notify: SnotifyService,
		private router: Router,
		private layoutNavigationService: LayoutNavigationService
	) {}

	ngOnInit() {
		if (this.pactoLayoutSDKWrapper) {
			this.subscription = this.pactoLayoutSDKWrapper
				.getConfig()
				.subscribe((config) => {
					this.configuracao = config;
					this.cd.detectChanges();
				});
		} else {
			this.configuracao = new PlataformaMenuV2Config();
		}
		setTimeout(() => {
			this.obterUsuarioGeral();
		}, 800);
	}

	ngOnDestroy() {
		if (this.subscription) {
			this.subscription.unsubscribe();
		}
	}

	public get trocaEmpresa(): TrocaEmpresa[] {
		if (this.listaTrocaEmpresas && this.listaTrocaEmpresas.length > 0) {
			return this.listaTrocaEmpresas;
		}
		const mapa = new Map<string, TrocaEmpresa>();
		for (const empresa of this.pactoLayoutSDKWrapper.empresasAcesso) {
			if (empresa.key !== this.pactoLayoutSDKWrapper.companyKey()) {
				mapa.set(
					empresa.key + "-" + empresa.emp,
					new TrocaEmpresa(empresa, null, null)
				);
			}
		}
		for (const empresa of this.pactoLayoutSDKWrapper.sessionService.empresas) {
			const troca = new TrocaEmpresa(
				null,
				empresa,
				this.pactoLayoutSDKWrapper.companyKey()
			);
			troca.atual =
				troca.empresa === +this.pactoLayoutSDKWrapper.loggedCompanyId;
			mapa.set(troca.chave + "-" + troca.empresa, troca);
		}
		const lista = [];
		for (const id of mapa.keys()) {
			lista.push(mapa.get(id));
		}
		lista.sort((a, b) => {
			if (a.nomeApresentar < b.nomeApresentar) {
				return -1;
			}
			if (a.nomeApresentar > b.nomeApresentar) {
				return 1;
			}
			return 0;
		});
		this.listaTrocaEmpresas = lista;
		return lista;
	}

	public get trocaEmpresaFiltradas() {
		const filter = this.fc.value;
		const empresasUnicas = new Set();
		let empresas;

		if (filter) {
			empresas = this.trocaEmpresa.filter((i) => {
				const nameN = i.nomeApresentar.toLowerCase();
				const filterN = filter.toLowerCase();
				return nameN.includes(filterN);
			});
		} else {
			empresas = this.trocaEmpresa;
		}

		const listEmpresas = [];

		const indexDaEmpresaLogada = empresas.findIndex((empresa) => empresa.atual);

		listEmpresas.push(empresas[indexDaEmpresaLogada]);

		for (const empresa of empresas) {
			if (!empresa.atual) {
				listEmpresas.push(empresa);
			}
		}

		listEmpresas.forEach((item) => {
			empresasUnicas.add(item);
		});

		return empresasUnicas;
	}

	public logoutHandler(): void {
		this.pactoLayoutSDKWrapper.sessionService.logOut();
	}

	public trocaClickHandler(troca: TrocaEmpresa): void {
		if (troca.novo) {
			this.loginAppApiLogin
				.validarTrocaEmpresa(this.usuarioGeral, troca)
				.subscribe((res) => {
					if (res.content) {
						this.logoutHandler();
						window.location.href = res.content;
					} else {
						this.notify.error(res.meta.message);
					}
				});
		} else if (
			troca.empresa.toString() !== this.pactoLayoutSDKWrapper.loggedCompanyId
		) {
			this.pactoLayoutSDKWrapper.sessionService.empresaId =
				troca.empresa.toString();
			this.updateLocalStorage();
			this.modal.dismissAll();
		}
	}

	public fecharHandler(): void {
		this.modal.dismissAll();
	}

	private updateLocalStorage(): void {
		const userLoged: LoginUrlQueries =
			this.pactoLayoutSDKWrapper.getLocalStorageParams();
		userLoged.empresaId = this.pactoLayoutSDKWrapper.loggedCompanyId.toString();
		this.pactoLayoutSDKWrapper.localStorageParamsService.setLocalStorageParams(
			userLoged
		);

		try {
			const internalLink = this.layoutNavigationService.getCurrentModule(
				userLoged.moduleId
			).route.internalLink;
			this.router.navigate([internalLink]).then(() => {
				window.location.reload();
			});
		} catch (e) {
			console.error("Erro ao trocar de empresa: ", e);
			window.location.reload();
		}
	}

	public changeUnitHandler(): void {
		this.modal.open(this.mudarEmpresa, {
			centered: true,
			windowClass: "larguraModal",
		});
	}

	private obterUsuarioGeral(): void {
		const { chave, codUsuarioZW, loggedUser } =
			this.pactoLayoutSDKWrapper.localStorageParamsService.getLocalStorageParams();
		const globalUser = localStorage.getItem("global-user");
		if (globalUser) {
			this.usuarioGeral = globalUser;
			this.preencherEmpresasAcesso(this.usuarioGeral);
			return;
		}
		this.loginAppApiLogin
			.obterUsuarioGeral(chave, codUsuarioZW, loggedUser)
			.subscribe(
				(res) => {
					if (res && res.content) {
						this.usuarioGeral = res.content;
						this.preencherEmpresasAcesso(this.usuarioGeral);
					}
				},
				(error) => console.error(error)
			);
	}

	public get multiUnidade(): boolean {
		return (
			(this.pactoLayoutSDKWrapper.sessionService.empresas &&
				this.pactoLayoutSDKWrapper.sessionService.empresas.length > 1) ||
			(this.pactoLayoutSDKWrapper.sessionService.empresasAcesso &&
				this.pactoLayoutSDKWrapper.sessionService.empresasAcesso.length > 1)
		);
	}

	private preencherEmpresasAcesso(usuarioGeral: string): void {
		this.loginAppApiLogin.obterEmpresas(usuarioGeral).subscribe(
			(res) => {
				if (res && res.content) {
					this.pactoLayoutSDKWrapper.sessionService.empresasAcesso =
						res.content;
					this.cd.detectChanges();
				}
			},
			(error) => console.error(error)
		);
	}
}
