import { CentroCusto } from "../centro_custo/centro-custo.model";
import { PlanoConta } from "../plano-conta/plano-conta.model";
import { Conta } from "../conta/conta.model";
import { Pessoa } from "../pessoa/pessoa.model";

export class ConfiguracaoFinanceiro {
	planocontastaxa: number;
	planoContaTaxaRelacionado: PlanoConta;
	centrocustostaxa: number;
	centroCustoTaxaRelacionado: CentroCusto;
	usarCentralEventos: boolean;
	usarmovimentacaocontas: boolean;
	solicitaSenhaLancarConta: boolean;
	permitirContaOutraUnidade: boolean;
	especificarCompetencia: boolean;
	habilitarExportacaoAlterdata: boolean;
	fecharCaixaAutomaticamente: boolean;
	mensagemBloqueio: string;
	bloquearAlunoChequeDevolvido: boolean;
	planoContasDevolucao: number;
	planoContasDevolucaoRelacionado: PlanoConta;
	adicionarDevolucaoRelatorioComissao: boolean;
	centroCustoDevolucao: number;
	centroCustoDevolucaoRelacionado: CentroCusto;
	planoContasTaxaBoleto: number;
	planoContasTaxaBoletoRelacionado: PlanoConta;
	centroCustosTaxaBoleto: number;
	centroCustosTaxaBoletoRelacionado: CentroCusto;
	cnpjObrigatorioFornecedor: boolean;
	contaPagarCompraEstoque: boolean;
	contapagarrecebercolabinativo: boolean;
	criarContaPagarAutomatico: boolean;
	contaCriarContaPagarAutomatico: number;
	apresentarValorPago: boolean;
	permitirTipoPlanoContaFilho: boolean;
	metaFinanceiraPorFaturamento: boolean;
	alterarDtPgtoZwAutomaticamenteConc: boolean;

	movimentacaoAutomaticaRecebiveisConciliacao: boolean;
	descricaoMovimentacaoAutomaticaDebito: string;
	descricaoMovimentacaoAutomaticaCredito: string;

	contaMovimentacaoAutomaticaDebito: number;
	contaMovimentacaoAutomaticaDebitoRelacionado: Conta;

	contaMovimentacaoAutomaticaCredito: number;
	contaMovimentacaoAutomaticaCreditoRelacionado: Conta;

	favorecidoMovimentacaoAutomaticaDebito: number;
	favorecidoMovimentacaoAutomaticaDebitoRelacionado: Pessoa;

	favorecidoMovimentacaoAutomaticaCredito: number;
	favorecidoMovimentacaoAutomaticaCreditoRelacionado: Pessoa;

	contaCriarContaPagarAutomaticoRelacionado: Conta;

	planoContasLancamentoAutomaticoSaida: number;
	planoContasLancamentoAutomaticoSaidaRelacionado: PlanoConta;

	planoContasLancamentoAutomaticoEntrada: number;
	planoContasLancamentoAutomaticoEntradaRelacionado: PlanoConta;

	centroCustoLancamentoAutomaticoSaida: number;
	centroCustoLancamentoAutomaticoSaidaRelacionado: CentroCusto;

	centroCustoLancamentoAutomaticoEntrada: number;
	centroCustoLancamentoAutomaticoEntradaRelacionado: CentroCusto;
}
