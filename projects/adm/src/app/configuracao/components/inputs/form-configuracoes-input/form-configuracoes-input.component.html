<ng-container
	*ngIf="config.hide === undefined || config.hide(inputs) === false"
	[ngTemplateOutletContext]="{ config: config }"
	[ngTemplateOutlet]="configurationItem">
	<div *ngIf="showChildren()" class="config-children">
		<ng-container *ngFor="let child of config.children">
			<ng-container
				*ngIf="child.hide === undefined || child.hide(inputs) === false"
				[ngTemplateOutletContext]="{ config: child }"
				[ngTemplateOutlet]="configurationItem"></ng-container>
		</ng-container>
	</div>
</ng-container>

<ng-template #configurationItem let-config="config">
	<div class="configuration-item">
		<div
			*ngIf="
				config.type !== 'grid' &&
				config.type !== 'grideditable' &&
				config.type !== 'inputgrid' &&
				config.type !== 'groupgridtable' &&
				config.type !== 'actions'
			"
			[class]="'d-flex row-config ' + config.customClass"
			style="flex-wrap: wrap">
			<div
				[ngClass]="
					config.type === 'radio'
						? 'col-md-4 col-text'
						: config.type === 'checkbox'
						? 'col-md-11 col-text'
						: 'col-md-7 col-text'
				">
				<div
					*ngIf="config?.title"
					[innerHTML]="title(config) | mark : searchText"
					[ngClass]="{
						'config-title': description(config),
						'config-title-no-description': !description(config),
						'row-config-no-description': !description(config)
					}"></div>
				<div
					*ngIf="description(config)"
					[innerHTML]="description(config) | mark : searchText"
					class="config-description"></div>
			</div>
			<div *ngIf="config.size === 'large'" class="row">
				<div class="col-md-12" style="width: 1200px; min-height: 40px">
					<pacto-base-input-form-configuracoes
						[internalFormGroup]="config?.formGroup"
						[control]="config?.formControl"
						[dateFilter]="config?.dateFilter"
						[disabled]="config?.disabled"
						[endpointUrl]="config?.endpointUrl"
						[errorMsg]="config?.errorMsg"
						[idKey]="config?.idKey"
						[id]="config?.name"
						[initOption]="config?.initOption"
						[labelKey]="config?.labelKey"
						[label]="config?.title"
						[max]="config?.max"
						[maxlength]="config?.maxlength"
						[min]="config?.min"
						[name]="config.name"
						[onClick]="config?.onClick"
						[options]="config?.options"
						[paramBuilder]="config?.paramBuilder"
						[placeholder]="config?.placeholder"
						[responseParser]="config?.responseParser"
						[rows]="config?.rows"
						[size]="config?.size"
						[textMask]="config?.textMask"
						[typeDescription]="config?.typeDescription"
						[typeButton]="config.btnfuncion?.typeButton"
						[type]="config.type"
						[icon]="config?.icon"></pacto-base-input-form-configuracoes>
				</div>
				<!-- <div  *ngIf="config?.historic" class="col-md-1" style="width: 20px;">
					<img
						class="icone-historico"
						src="assets/images/historico_totalpass.svg"
						(click)="onHistoricClick()"
						style="cursor: pointer;"
						title="Ver histórico" />
				</div> -->
			</div>
			<div
				*ngIf="config.size !== 'large' && !isRangeInput(config)"
				[ngClass]="
					config.type === 'checkbox' || config.type === 'radio'
						? 'col-md-auto'
						: config.type === 'number'
						? 'col-md-4 offset-md-1'
						: 'col-md-4 offset-md-1'
				">
				<pacto-base-input-form-configuracoes
					[internalFormGroup]="config?.formGroup"
					[control]="config?.formControl"
					[dateFilter]="config?.dateFilter"
					[disabled]="config?.disabled"
					[endpointUrl]="config?.endpointUrl"
					[errorMsg]="config?.errorMsg"
					[idKey]="config?.idKey"
					[id]="config?.name"
					[initOption]="config?.initOption"
					[labelKey]="config?.labelKey"
					[label]="config?.title"
					[max]="config?.max"
					[maxlength]="config?.maxlength"
					[min]="config?.min"
					[name]="config.name"
					[onClick]="config?.onClick"
					[options]="config?.options"
					[paramBuilder]="config?.paramBuilder"
					[placeholder]="config?.placeholder"
					[responseParser]="config?.responseParser"
					[rows]="config?.rows"
					[size]="config?.size"
					[textMask]="config?.textMask"
					[typeDescription]="config?.typeDescription"
					[typeButton]="config.btnfuncion?.typeButton"
					[type]="config.type"
					[formatos]="config?.formatos"
					[formatosValidos]="config?.formatosValidos"
					[urlImage]="config?.urlImage"
					[icon]="config?.icon"></pacto-base-input-form-configuracoes>
			</div>

			<div
				*ngIf="isRangeInput(config)"
				class="col-md-4 offset-md-1 input-periodo">
				<pacto-base-input-form-configuracoes
					*ngFor="let child of config.children"
					[internalFormGroup]="config?.formGroup"
					[control]="child?.formControl"
					[dateFilter]="child?.dateFilter"
					[disabled]="child?.disabled"
					[errorMsg]="child?.errorMsg"
					[id]="child?.name"
					[label]="child?.title"
					[max]="child?.max"
					[maxlength]="child?.maxlength"
					[min]="child?.min"
					[name]="child.name"
					[onClick]="child?.onClick"
					[options]="child?.options"
					[textMask]="child?.textMask"
					[typeDescription]="child?.typeDescription"
					[typeButton]="config.btnfuncion?.typeButton"
					[formatos]="config?.formatos"
					[formatosValidos]="config?.formatosValidos"
					[urlImage]="config?.urlImage"
					[type]="child.type"></pacto-base-input-form-configuracoes>
			</div>
		</div>

		<div *ngIf="config.type === 'grid'" class="row row-config">
			<div class="col-md-12">
				<div class="row row-grid-title">
					<div
						*ngIf="config?.title"
						[innerHTML]="title(config) | mark : searchText"
						class="config-title"></div>
					<div
						*ngIf="description(config)"
						[innerHTML]="description(config) | mark : searchText"
						class="config-description"></div>
				</div>
				<div class="row row-grid">
					<div class="grid-container">
						<pacto-relatorio
							#tableComponent
							[alternatingColors]="config?.alternatingColors"
							[emptyStateMessage]="config?.emptyStateMessage"
							[showShare]="false"
							[table]="config?.dataGridConfig"></pacto-relatorio>
					</div>
				</div>
			</div>
		</div>

		<div *ngIf="config.type === 'grideditable'" class="row row-config">
			<div class="col-md-12">
				<div class="row row-grid-title">
					<div
						*ngIf="config?.title"
						[innerHTML]="title(config) | mark : searchText"
						class="config-title"></div>
					<div
						*ngIf="description(config)"
						[innerHTML]="description(config) | mark : searchText"
						class="config-description"></div>
				</div>
				<div class="row row-grid">
					<div class="grid-container">
						<pacto-cat-table-editable
							#tableComponent
							[isEditable]="(config?.isEditable || true) && !config?.disabled"
							[newLineTitle]="config?.newLineTitle"
							[showAddRow]="!config?.disabled"
							[showDelete]="(config?.showDelete || true) && !config?.disabled"
							[showEdit]="(config?.showEdit || true) && !config?.disabled"
							[isAddRowAvailable]="
								config?.isAddRowAvailable && !config?.disabled
							"
							[table]="config?.dataGridConfig"></pacto-cat-table-editable>
					</div>
				</div>
			</div>
		</div>
		<div *ngIf="config.type === 'inputgrid'" class="row row-config">
			<div class="col-md-12">
				<div class="row row-grid-title">
					<div
						*ngIf="config?.title"
						[innerHTML]="title(config) | mark : searchText"
						class="config-title"></div>
					<div
						*ngIf="description(config)"
						[innerHTML]="description(config) | mark : searchText"
						class="config-description"></div>
				</div>
				<div class="row row-grid">
					<div class="grid-container">
						<div class="row">
							<div *ngIf="config?.inputText" class="col-md-6">
								<pacto-base-input-form-configuracoes
									class="isVisible"
									[internalFormGroup]="config?.formGroup"
									[control]="config.inputText?.formControl"
									[dateFilter]="config.inputText?.dateFilter"
									[disabled]="config.inputText?.disabled"
									[endpointUrl]="config.inputText?.endpointUrl"
									[errorMsg]="config.inputText?.errorMsg"
									[idKey]="config.inputText?.idKey"
									[id]="config.inputText?.name"
									[initOption]="config.inputText?.initOption"
									[labelKey]="config.inputText?.labelKey"
									[label]="config.inputText?.title"
									[max]="config.inputText?.max"
									[maxlength]="config.inputText?.maxlength"
									[min]="config.inputText?.min"
									[name]="config.inputText.name"
									[onClick]="config.inputText?.onClick"
									[options]="config.inputText?.options"
									[paramBuilder]="config.inputText?.paramBuilder"
									[placeholder]="config.inputText?.placeholder"
									[responseParser]="config.inputText?.responseParser"
									[rows]="config.inputText?.rows"
									[size]="config.inputText?.size"
									[textMask]="config.inputText?.textMask"
									[typeDescription]="config.inputText?.typeDescription"
									[typeButton]="config.btnfuncion?.typeButton"
									[type]="
										config.inputText.type
									"></pacto-base-input-form-configuracoes>
							</div>

							<div *ngIf="config?.sincronizar" class="sinc-btn col-md-1 mt-4">
								<pacto-base-input-form-configuracoes
									class="isVisible"
									[control]="config.sincronizar?.formControl"
									[dateFilter]="config.sincronizar?.dateFilter"
									[disabled]="config.sincronizar?.disabled"
									[endpointUrl]="config.sincronizar?.endpointUrl"
									[errorMsg]="config.sincronizar?.errorMsg"
									[idKey]="config.sincronizar?.idKey"
									[id]="config.sincronizar?.name"
									[initOption]="config.sincronizar?.initOption"
									[labelKey]="config.sincronizar?.labelKey"
									[label]="config.sincronizar?.title"
									[max]="config.sincronizar?.max"
									[maxlength]="config.sincronizar?.maxlength"
									[min]="config.sincronizar?.min"
									[name]="config.sincronizar.name"
									[onClick]="config.sincronizar?.onClick"
									[options]="config.sincronizar?.options"
									[paramBuilder]="config.sincronizar?.paramBuilder"
									[placeholder]="config.sincronizar?.placeholder"
									[responseParser]="config.sincronizar?.responseParser"
									[rows]="config.sincronizar?.rows"
									[size]="config.sincronizar?.size"
									[textMask]="config.sincronizar?.textMask"
									[typeDescription]="config.sincronizar.typeDescription"
									[typeButton]="config.sincronizar?.typeButton"
									[type]="config.sincronizar?.type"
									[icon]="
										config.sincronizar?.icon
									"></pacto-base-input-form-configuracoes>
							</div>
							<div *ngIf="config?.select" class="col-md-5 mt-2">
								<span class="nome">{{ config.select?.title }}</span>
								<pacto-base-input-form-configuracoes
									class="isVisible"
									[internalFormGroup]="config?.formGroup"
									[control]="config.select?.formControl"
									[dateFilter]="config.select?.dateFilter"
									[disabled]="config.select?.disabled"
									[endpointUrl]="config.select?.endpointUrl"
									[errorMsg]="config.select?.errorMsg"
									[idKey]="config.select?.idKey"
									[id]="config.select?.name"
									[initOption]="config.select?.initOption"
									[labelKey]="config.select?.labelKey"
									[label]="config.select?.title"
									[max]="config.select?.max"
									[maxlength]="config.select?.maxlength"
									[min]="config.select?.min"
									[name]="config.select?.name"
									[onClick]="config.select?.onClick"
									[options]="config.select?.options"
									[paramBuilder]="config.select?.paramBuilder"
									[placeholder]="config.select?.placeholder"
									[responseParser]="config.select?.responseParser"
									[rows]="config.select?.rows"
									[size]="config.select?.size"
									[textMask]="config.select?.textMask"
									[typeDescription]="config.select?.typeDescription"
									[typeButton]="config.btnfuncion?.typeButton"
									[type]="
										config.select?.type
									"></pacto-base-input-form-configuracoes>
							</div>
						</div>
						<!--text-area-->
						<div *ngIf="config?.textarea">
							<span class="nome">{{ config.textarea?.title }}</span>
							<pacto-base-input-form-configuracoes
								class="isVisible"
								[internalFormGroup]="config?.textarea?.formGroup"
								[control]="config.textarea?.formControl"
								[dateFilter]="config.textarea?.dateFilter"
								[disabled]="config.textarea?.disabled || config?.disabled"
								[endpointUrl]="config.textarea?.endpointUrl"
								[errorMsg]="config.textarea?.errorMsg"
								[idKey]="config.textarea?.idKey"
								[id]="config.textarea?.name"
								[initOption]="config.textarea?.initOption"
								[labelKey]="config.textarea?.labelKey"
								[label]="config.textarea?.title"
								[max]="config.textarea?.max"
								[maxlength]="config.textarea?.maxlength"
								[min]="config.textarea?.min"
								[name]="config.textarea?.name"
								[onClick]="config.textarea?.onClick"
								[options]="config.textarea?.options"
								[paramBuilder]="config.textarea?.paramBuilder"
								[placeholder]="config.textarea?.placeholder"
								[responseParser]="config.textarea?.responseParser"
								[rows]="config.textarea?.rows"
								[size]="config.textarea?.size"
								[textMask]="config.textarea?.textMask"
								[typeDescription]="config?.typeDescription"
								[type]="config.textarea?.type"
								[typeButton]="
									config.btnfuncion?.typeButton
								"></pacto-base-input-form-configuracoes>
						</div>

						<!--btn-funcion-->
						<div class="row row-grid-actions mt-2">
							<div *ngIf="config?.btnfuncion2" class="mr-4">
								<pacto-base-input-form-configuracoes
									[internalFormGroup]="config?.formGroup"
									[control]="config.btnfuncion2?.formControl"
									[dateFilter]="config.btnfuncion2?.dateFilter"
									[disabled]="config.btnfuncion2?.disabled"
									[endpointUrl]="config.btnfuncion2?.endpointUrl"
									[errorMsg]="config.btnfuncion2?.errorMsg"
									[idKey]="config.btnfuncion2?.idKey"
									[id]="config.btnfuncion2?.name"
									[initOption]="config.btnfuncion2?.initOption"
									[labelKey]="config.btnfuncion2?.labelKey"
									[label]="config.btnfuncion2?.title"
									[max]="config.btnfuncion2?.max"
									[maxlength]="config.btnfuncion2?.maxlength"
									[min]="config.btnfuncion2?.min"
									[name]="config.btnfuncion2?.name"
									[onClick]="config.btnfuncion2?.onClick"
									[options]="config.btnfuncion2?.options"
									[paramBuilder]="config.btnfuncion2?.paramBuilder"
									[placeholder]="config.btnfuncion2?.placeholder"
									[responseParser]="config.btnfuncion2?.responseParser"
									[rows]="config.btnfuncion2?.rows"
									[size]="config.btnfuncion2?.size"
									[textMask]="config.btnfuncion2?.textMask"
									[typeDescription]="config.btnfuncion2?.typeDescription"
									[type]="config.btnfuncion2?.type"
									[typeButton]="
										config.btnfuncion2?.typeButton
									"></pacto-base-input-form-configuracoes>
							</div>
							<div *ngIf="config?.btnfuncion" class="mr-4">
								<pacto-base-input-form-configuracoes
									[internalFormGroup]="config?.formGroup"
									[control]="config.btnfuncion?.formControl"
									[dateFilter]="config.btnfuncion?.dateFilter"
									[disabled]="config.btnfuncion?.disabled"
									[endpointUrl]="config.btnfuncion?.endpointUrl"
									[errorMsg]="config.btnfuncion?.errorMsg"
									[idKey]="config.btnfuncion?.idKey"
									[id]="config.btnfuncion?.name"
									[initOption]="config.btnfuncion?.initOption"
									[labelKey]="config.btnfuncion?.labelKey"
									[label]="config.btnfuncion?.title"
									[max]="config.btnfuncion?.max"
									[maxlength]="config.btnfuncion?.maxlength"
									[min]="config.btnfuncion?.min"
									[name]="config.btnfuncion?.name"
									[onClick]="config.btnfuncion?.onClick"
									[options]="config.btnfuncion?.options"
									[paramBuilder]="config.btnfuncion?.paramBuilder"
									[placeholder]="config.btnfuncion?.placeholder"
									[responseParser]="config.btnfuncion?.responseParser"
									[rows]="config.btnfuncion?.rows"
									[size]="config.btnfuncion?.size"
									[textMask]="config.btnfuncion?.textMask"
									[typeDescription]="config.btnfuncion?.typeDescription"
									[type]="config.btnfuncion?.type"
									[typeButton]="
										config.btnfuncion?.typeButton
									"></pacto-base-input-form-configuracoes>
							</div>
							<div *ngIf="config?.grids" class="col-md-12">
								<div *ngFor="let grid of config.grids">
									<span class="nome mt-2">{{ grid?.title }}</span>
									<div class="grid-container">
										<pacto-cat-table-editable
											#tableComponent
											[isEditable]="
												(grid?.isEditable || true) && !config?.disabled
											"
											[newLineTitle]="grid?.newLineTitle"
											[showAddRow]="
												(grid?.showAddRow || true) && !config?.disabled
											"
											[showDelete]="
												(grid?.showDelete || true) && !config?.disabled
											"
											[showEdit]="(grid?.showEdit || true) && !config?.disabled"
											[table]="grid?.dataGridConfig"></pacto-cat-table-editable>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div *ngIf="config.type === 'groupgridtable'" class="row row-config">
			<div class="col-md-12">
				<div class="row row-grid-title">
					<div
						*ngIf="config?.title"
						[innerHTML]="title(config) | mark : searchText"
						class="config-title"></div>
					<div
						*ngIf="description(config)"
						[innerHTML]="description(config) | mark : searchText"
						class="config-description"></div>
				</div>

				<div class="row row-grid">
					<div class="grid-container">
						<div *ngIf="config?.grids" class="col-md-12">
							<div *ngFor="let grid of config.grids">
								<span class="nome mt-2">{{ grid?.title }}</span>
								<div class="grid-container">
									<pacto-cat-table-editable
										#tableComponent
										[isEditable]="
											(grid?.isEditable || true) && !config?.disabled
										"
										[newLineTitle]="grid?.newLineTitle"
										[showAddRow]="!config?.disabled"
										[showDelete]="
											(grid?.showDelete || true) && !config?.disabled
										"
										[showEdit]="(grid?.showEdit || true) && !config?.disabled"
										[table]="grid?.dataGridConfig"></pacto-cat-table-editable>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div *ngIf="config.type === 'actions'" class="row row-config">
			<div class="col-md-12">
				<div class="row row-grid-title">
					<div
						*ngIf="config?.title"
						[innerHTML]="title(config) | mark : searchText"
						class="config-title"></div>
					<div
						*ngIf="description(config)"
						[innerHTML]="description(config) | mark : searchText"
						class="config-description"></div>
				</div>

				<div
					class="row row-grid-actions mt-2"
					style="display: flex; align-items: start; gap: 1rem">
					<div *ngIf="config?.link" style="flex-shrink: 0">
						<a
							[href]="config.link"
							class="link-class ml-4"
							target="_blank"
							rel="noopener noreferrer">
							<span style="color: rgb(0, 134, 105)">
								<b>Conectado</b>
								:
							</span>
							{{ config.link }}
						</a>
					</div>
					<div style="margin-left: auto; display: flex; gap: 1rem">
						<div *ngIf="config?.btnfuncion2" class="mr-4">
							<pacto-base-input-form-configuracoes
								[internalFormGroup]="config?.formGroup"
								[control]="config.btnfuncion2?.formControl"
								[dateFilter]="config.btnfuncion2?.dateFilter"
								[disabled]="config.btnfuncion2?.disabled"
								[endpointUrl]="config.btnfuncion2?.endpointUrl"
								[errorMsg]="config.btnfuncion2?.errorMsg"
								[idKey]="config.btnfuncion2?.idKey"
								[id]="config.btnfuncion2?.name"
								[initOption]="config.btnfuncion2?.initOption"
								[labelKey]="config.btnfuncion2?.labelKey"
								[label]="config.btnfuncion2?.title"
								[max]="config.btnfuncion2?.max"
								[maxlength]="config.btnfuncion2?.maxlength"
								[min]="config.btnfuncion2?.min"
								[name]="config.btnfuncion2?.name"
								[onClick]="config.btnfuncion2?.onClick"
								[options]="config.btnfuncion2?.options"
								[paramBuilder]="config.btnfuncion2?.paramBuilder"
								[placeholder]="config.btnfuncion2?.placeholder"
								[responseParser]="config.btnfuncion2?.responseParser"
								[rows]="config.btnfuncion2?.rows"
								[size]="config.btnfuncion2?.size"
								[textMask]="config.btnfuncion2?.textMask"
								[typeDescription]="config.btnfuncion2?.typeDescription"
								[type]="config.btnfuncion2?.type"
								[typeButton]="config.btnfuncion2?.typeButton"
								[icon]="
									config.btnfuncion2?.icon
								"></pacto-base-input-form-configuracoes>
						</div>
						<div *ngIf="config?.btnfuncion" class="mr-4">
							<pacto-base-input-form-configuracoes
								[internalFormGroup]="config?.formGroup"
								[control]="config.btnfuncion?.formControl"
								[dateFilter]="config.btnfuncion?.dateFilter"
								[disabled]="config.btnfuncion?.disabled"
								[endpointUrl]="config.btnfuncion?.endpointUrl"
								[errorMsg]="config.btnfuncion?.errorMsg"
								[idKey]="config.btnfuncion?.idKey"
								[id]="config.btnfuncion?.name"
								[initOption]="config.btnfuncion?.initOption"
								[labelKey]="config.btnfuncion?.labelKey"
								[label]="config.btnfuncion?.title"
								[max]="config.btnfuncion?.max"
								[maxlength]="config.btnfuncion?.maxlength"
								[min]="config.btnfuncion?.min"
								[name]="config.btnfuncion?.name"
								[onClick]="config.btnfuncion?.onClick"
								[options]="config.btnfuncion?.options"
								[paramBuilder]="config.btnfuncion?.paramBuilder"
								[placeholder]="config.btnfuncion?.placeholder"
								[responseParser]="config.btnfuncion?.responseParser"
								[rows]="config.btnfuncion?.rows"
								[size]="config.btnfuncion?.size"
								[textMask]="config.btnfuncion?.textMask"
								[typeDescription]="config.btnfuncion?.typeDescription"
								[type]="config.btnfuncion?.type"
								[typeButton]="config.btnfuncion?.typeButton"
								[icon]="config.btnfuncion?.icon">
								>
							</pacto-base-input-form-configuracoes>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</ng-template>
