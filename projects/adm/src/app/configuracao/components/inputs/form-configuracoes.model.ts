import {
	AbstractControl,
	AbstractControlOptions,
	FormGroup,
	ValidatorFn,
} from "@angular/forms";
import { SubGrupoInputs } from "../config/model/sub-group-inputs.model";
import {
	PactoDataGridConfig,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
} from "ui-kit";

export type ConfigItem =
	| ConfigItemBase
	| ConfigItemCheckbox
	| ConfigItemRadio
	| ConfigItemSelect
	| ConfigItemSelectFilter
	| ConfigItemMultiSelectFilter
	| ConfigItemSwitch
	| ConfigItemButton
	| ConfigItemText
	| ConfigItemGrid
	| ConfigItemGridEditable
	| ConfigItemNumber
	| ConfigItemDate
	| ConfigItemInputGrid
	| ConfigItemTextArea
	| ConfigItemGroupGridTable
	| ConfigItemDate
	| ConfigInputFile;

export interface ConfigItemBase {
	name?: string;
	fieldLogName?: string;
	entityLogName?: string;
	tagLog?: string;
	codigo?: string | boolean;
	title?: string;
	descriptionMode?: string;
	description?: string | (() => string);
	formControl?: AbstractControl;
	autoControlCurrentValue?: boolean;
	validators?: ValidatorFn | ValidatorFn[] | AbstractControlOptions | null;
	errorMsg?: string;
	formGroup?: FormGroup;
	type: ConfigItemType;
	hide?: (inputs: SubGrupoInputs[]) => boolean;
	children?: ConfigItem[];
	size?: string;
	rows?: number;
	typeButton?: string;
	id?: string;
	disabled?: boolean | (() => boolean);
	icon?: string;
	customClass?: string;
	actions?: ConfigItem[];
	link?: string;
	historic?: boolean;
	onHistoricClick?: () => void;
}

export enum ConfigItemType {
	CHECKBOX = "checkbox",
	DATE = "date",
	TEXT = "text",
	TEXT_AREA = "textarea",
	RADIO = "radio",
	SELECT = "select",
	SELECTFILTER = "selectfilter",
	MULTISELECTFILTER = "multiselectfilter",
	NUMBER = "number",
	BUTTON = "button",
	SWITCH = "switch",
	GROUP = "group",
	RANGE = "range",
	GRID = "grid",
	GRIDEDITABLE = "grideditable",
	INPUT_GRID = "inputgrid",
	PASSWORD = "password",
	GROUP_GRID_TABLE = "groupgridtable",
	ACTIONS = "actions",
	INPUTFILE = "inputfile",
}

export interface ConfigItemDate extends ConfigItemBase {
	dateFilter?: (date: Date) => boolean;
}

export interface ConfigItemCheckbox extends ConfigItemBase {
	checkbox: true;
}

export interface ConfigItemSwitch extends ConfigItemBase {
	switch: true;
}

export interface ConfigItemText extends ConfigItemBase {
	textMask?: Array<RegExp | string>;
	maxlength?: number;
}

export interface ConfigItemGrid extends ConfigItemBase {
	dataGridConfig?: PactoDataGridConfig;
	alternatingColors?: "first" | "second" | "none";
	emptyStateMessage?: string;
}

export interface ConfigItemGridEditable extends ConfigItemBase {
	dataGridConfig?: PactoDataGridConfig;
	newLineTitle?: string;
	showEdit?: boolean;
	showDelete?: boolean;
	emptyStateMessage?: string;
	reloadData?: () => void;
}

export interface ConfigItemNumber extends ConfigItemBase {
	textMask?: Array<RegExp | string>;
	maxlength?: number;
	min?: number;
	max?: number;
	placeholder: string;
	isAddRowAvailable?: boolean;
}

export interface ConfigItemRadio extends ConfigItemBase {
	options: ConfigItemRadioOption[];
}

export interface ConfigItemSelect extends ConfigItemBase {
	options: ConfigItemSelectOption[];
}

export interface ConfigItemSelectFilter extends ConfigItemBase {
	endpointUrl: string;
	placeholder: string;
	responseParser: SelectFilterResponseParser;
	paramBuilder: SelectFilterParamBuilder;
	labelKey: string;
	idKey: string;
}

export interface ConfigItemMultiSelectFilter extends ConfigItemBase {
	endpointUrl?: string;
	placeholder: string;
	responseParser?: SelectFilterResponseParser;
	paramBuilder?: SelectFilterParamBuilder;
	labelKey: string;
	idKey: string;
	options?: Object[];
	initOption?: Object;
}

export interface ConfigItemButton extends ConfigItemBase {
	typeDescription: string;
	onClick: (() => void) | undefined;
}

export interface ConfigItemInputGrid extends ConfigItemBase {
	inputText?: ConfigItem;
	select?: ConfigItem;
	textarea?: ConfigItem;
	btnfuncion?: ConfigItem;
	btnfuncion2?: ConfigItem;
	grids?: ConfigItemGridEditable[];
	sincronizar?: ConfigItem;
}

export interface ConfigItemGroupGridTable extends ConfigItemBase {
	grids?: ConfigItemGridEditable[];
}

export interface ConfigItemTextArea extends ConfigItemBase {
	size?: string;
	rows?: number;
}
/**
 * OPTIONS
 */
export interface ConfigItemSelectOption {
	id: any;
	label: string;
}

export interface ConfigItemRadioOption {
	name: string;
	value: any;
}

export interface ConfigItemActions {
	btnfuncion?: ConfigItem;
	btnfuncion2?: ConfigItem;
	actions?: ConfigItem[];
	link?: string;
}

export interface ConfigInputFile extends ConfigItemBase {
	formatos: string;
	urlImage: string;
	formatosValidos: RegExp;
}
