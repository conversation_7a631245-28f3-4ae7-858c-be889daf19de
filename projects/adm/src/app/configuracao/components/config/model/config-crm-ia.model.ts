export interface ConfiguracaoIA {
	codigo?: number;
	codigoEmpresa?: number;
	personalidade?: string;
	informacoesAdicionaisAcademia?: string;
	habilitarconfigia?: boolean;
	idInstanciaZApi?: string;
	loginPactoConversas?: string;
	senhaPactoConversas?: string;
	tokenZApi?: string;
	whatsappBusiness?: boolean;
	horarioPadrao?: string;
	emailResponsavelConversasAI?: string;
	telefoneResponsavelConversasAI?: string;
	configuracaoGymbot: {
		tokenGymbot?: string;
		habilitarGymbot?: boolean;
		descricaoDepartamento?: string;
		InstrucaoDepartamento?: string;
	};
	configuracaoRede: {
		codigo?: number;
		codigoUnidadeMatriz?: number;
		chaveBancoMatriz?: string;
		tipoConfigRede?: string;
	};
	descricaoNotificacaoProativo?: string;
	desabilitarAgendamentoAulasExperimentais?: boolean;
}

export interface ConfiguracaoCrmFaseIA {
	codigo?: number;
	fase?: string;
	descricao?: string;
	habilitar?: boolean;
	codigometaextra?: number;
	nomemetaextra?: string;
	mensagensextras?: string;
}

export interface MaladiretaEntity {
	mensagem: string;
	codigometaextra: string;
	titulo: string;
	remetente: number;
	modelomensagem?: number;
	codigo: number;
	empresa?: number;
	vigenteate: Date;
	evento?: number;
	sql: string;
	tipoagendamento?: number;
	excluida?: boolean;
	contatoavulso?: boolean;
	faseenvio: string;
	opcoes: string;
	tipopergunta?: number;
	codaberturameta?: number;
	diasposvenda?: number;
	metaextraindividual?: boolean;
	tipoconsultormetaextraindividual: string;
	quantidademinimaacessos?: number;
	quantidademaximaacessos?: number;
	intervalodias?: number;
	todasempresas?: boolean;
	questionario?: number;
	tipocancelamento?: number;
	importarlista?: boolean;
	configs: string;
	enviohabilitado?: boolean;
	smsmarketing?: boolean;
	statusentregabilidade?: boolean;
}

export type NotificationBeforeAfter = {
	message_instructions: string;
	time: number;
	unit: string;
};

export type NotificationAt = {
	message_instructions: string;
	time: string; // formato "08:00"
};

export type TypedNotification = {
	type: "antes" | "atual" | "depois";
	message_instructions: string;
	time: number | string;
	unit?: string;
};
