import { CurrencyPipe } from "@angular/common";
import {
	ChangeDetectorRef,
	Component,
	ElementRef,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	FormArray,
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { MatDialog, MatDialogRef } from "@angular/material";
import { ActivatedRoute, Router } from "@angular/router";
import { AdmCoreApiClienteService } from "adm-core-api";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
} from "adm-legado-api";
import moment from "moment";
import { SnotifyService } from "ng-snotify";
import { PermissaoService } from "pacto-layout";
import { PerfilRecursoPermissoTipo, SessionService } from "sdk";
import { DialogAutorizacaoAcessoComponent, LoaderService } from "ui-kit";
import {
	BodyPagamento,
	CaixaEmAbertoService,
} from "../caixa-em-aberto.service";
import { CaptalizePipe } from "@base-shared/pipe/captalize.pipe";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { ModalConfirmarRecebimentoComponent } from "@adm/caixa-em-aberto/receber-parcelas/modal-confirmar-recebimento/modal-confirmar-recebimento.component";

@Component({
	selector: "adm-receber-parcelas",
	templateUrl: "./receber-parcelas.component.html",
	styleUrls: ["./receber-parcelas.component.scss"],
})
export class ReceberParcelasComponent implements OnInit {
	@ViewChild("editDialog", { static: true })
	editDialog: TemplateRef<any>;
	toastDataDeRecebimentoDiferente = false;
	@ViewChild("finalizarDialog", { static: true })
	finalizarDialog: TemplateRef<any>;
	@ViewChild("reciboEmailDialog", { static: true })
	reciboEmailDialog: TemplateRef<any>;
	public parcelas: any = [];
	public grupos: any = [];
	public formasDePagamento: any[];
	public formasDePagamentoApresentar: any[];
	public pessoas: any = [
		{
			label: null,
			data: { matricula: null, nome: null, codigo: null, tipoComprador: null },
		},
	];
	public form: FormGroup;
	pixData;
	dialogRef: MatDialogRef<any, any>;
	pagamentoResponse: {
		status: "SUCESSO" | "ERRO";
		data: any;
		msg: any;
		recibo: any;
	} = {
		status: "SUCESSO",
		data: {},
		msg: "",
		recibo: null,
	};

	saldoCC = 0;
	apresentarSaldoCC = true;
	public formEmail: FormGroup = new FormGroup({
		email: new FormControl("", [Validators.email, Validators.required]),
	});
	public emails: string[] = [];
	permissao4_07: any;

	constructor(
		private readonly snotifyService: SnotifyService,
		private readonly router: Router,
		private loaderService: LoaderService,
		private readonly caixaAbertoService: CaixaEmAbertoService,
		private readonly fb: FormBuilder,
		private readonly elementRef: ElementRef,
		private permissaoService: PermissaoService,
		private currencyPipe: CurrencyPipe,
		private telaClienteService: AdmLegadoTelaClienteService,
		private dialogService: MatDialog,
		private cd: ChangeDetectorRef,
		private pactoModal: ModalService,
		private sessionService: SessionService,
		private activatedRoute: ActivatedRoute,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private msAdmCoreService: AdmCoreApiClienteService
	) {}

	ngOnInit(): void {
		this.permissao4_07 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "4.07"
			);
		const identificador = this.activatedRoute.snapshot.paramMap.get(
			"identificador-pagamento"
		);
		if (identificador) {
			this.buildForm2();

			let filters;
			if (identificador.startsWith("pagamentoVenda_")) {
				const vendaAvulsa = identificador.replace("pagamentoVenda_", "");
				filters = JSON.stringify({
					vendaAvulsa,
				});
			} else if (identificador.startsWith("pagamentoDiaria_")) {
				const diaria = identificador.replace("pagamentoDiaria_", "");
				filters = JSON.stringify({
					diaria,
				});
			} else if (identificador.startsWith("pagamentoContrato_")) {
				const contrato = identificador.replace("pagamentoContrato_", "");
				filters = JSON.stringify({
					contrato,
				});
			}

			if (filters) {
				this.caixaAbertoService
					.consultar(filters, "0", "50")
					.subscribe((resp) => {
						if (resp.length === 0) {
							this.snotifyService.error(
								"Houve um erro com as parcelas selecionadas, Retornando para a lista"
							);
							this.router.navigate(["adm", "caixa-em-aberto"]);
						}
						this.caixaAbertoService.grupos = resp;
						this.caixaAbertoService.parcelas = resp[0].parcelas;
						this.caixaAbertoService.empresaSelecionada = Number(
							this.sessionService.empresaId
						);
						this.carregarDados();
						this.cd.detectChanges();
					});
			} else {
				this.carregarDados();
			}
		} else {
			this.carregarDados();
		}
	}

	carregarDados() {
		this.parcelas = this.caixaAbertoService.parcelas;
		this.grupos = this.caixaAbertoService.grupos;
		if (!this.parcelas || !this.grupos) {
			this.snotifyService.error(
				"Houve um erro com as parcelas selecionadas, Retornando para a lista"
			);
			this.router.navigate(["adm", "caixa-em-aberto"]);
		}
		this.pessoas = this.grupos.map((grupo) => {
			return {
				label: grupo.pessoa.nome,
				data: {
					...grupo.pessoa,
					codigoCliente: grupo.cliente.codigo,
					matricula: grupo.cliente.matricula,
					tipoComprador: grupo.tipoComprador,
				},
			};
		});
		this.getFormasDePagamento();
		this.buildForm();
		this.formasDePagamentoForm.valueChanges.subscribe((event) => {
			// se so tiver um pode mudar pra qqrl coisa
			if (event.length === 1) {
				this.formasDePagamento.forEach((pag) => {
					pag.disabled = false;
				});
			}
			if (event.length > 1) {
				this.formasDePagamento.forEach((pag) => {
					pag.disabled = false;
					// bloquear outras formas de pagamento que só podem ir sozinhas
					if (pag.value.receberValorTotal) {
						pag.disabled = true;
					}
					// bloquear todas formas ja usadas exceto cartao offline(CA_OFF) e debito(CD) e dinheiro(AV)
					if (
						this.formasDePagamentoForm.value.some(
							(pagSelecionado) =>
								pagSelecionado.formaDePagamento.tipoFormaPagamento ===
									pag.value.tipoFormaPagamento &&
								!(
									pag.value.tipoFormaPagamento ===
										this.formPagamentoEnum.CARTAOCREDITO_OFFLINE ||
									pag.value.tipoFormaPagamento ===
										this.formPagamentoEnum.CARTAODEBITO ||
									pag.value.tipoFormaPagamento ===
										this.formPagamentoEnum.DINHEIRO
								)
						)
					) {
						pag.disabled = true;
					}
				});
			}
			this.formasDePagamentoApresentar = [];
			this.formasDePagamento.forEach((pag) => {
				if (!pag.disabled) {
					this.formasDePagamentoApresentar.push(pag);
				}
			});
			this.cd.detectChanges();
		});
		this.form.get("responsavel").valueChanges.subscribe((v) => {
			this.msAdmCoreService.dadosPessoais(v.matricula).subscribe((aluno) => {
				v.urlFoto = aluno.urlFoto;
				this.emails = [];
				this.getSaldoContaCorrente();
				this.cd.detectChanges();
			});
		});
		this.form.get("dataDeRecebimento").valueChanges.subscribe((v) => {
			this.toastDataDeRecebimentoDiferente =
				moment(v).startOf("day").diff(moment().startOf("day"), "day") !== 0;
		});
		this.getSaldoContaCorrente();
		this.cd.detectChanges();
	}

	buildForm2(): void {
		this.form = this.fb.group({
			dataDeRecebimento: this.fb.control(moment().format()),
			responsavel: this.fb.control(null),
			observacao: this.fb.control(""),
			cobrarMultaJuros: this.fb.control(true),
			debitoContaCorrente: this.fb.control(false),
			creditoContaCorrente: this.fb.control(false),
			valorDebitoContaCorrente: this.fb.control(0.0),
			valorCreditoContaCorrente: this.fb.control(0.0),
			formasDePagamento: this.fb.array([
				this.fb.group({
					formaDePagamento: this.fb.control("", Validators.required),
					valor: this.fb.control(0, Validators.required),
				}),
			]),
		});
	}

	buildForm(): void {
		this.form = this.fb.group({
			dataDeRecebimento: this.fb.control(moment().format()),
			responsavel: this.fb.control(this.pessoas[0].data),
			observacao: this.fb.control(""),
			cobrarMultaJuros: this.fb.control(true),
			debitoContaCorrente: this.fb.control(false),
			creditoContaCorrente: this.fb.control(false),
			valorDebitoContaCorrente: this.fb.control(0.0),
			valorCreditoContaCorrente: this.fb.control(0.0),
			formasDePagamento: this.fb.array([
				this.fb.group({
					formaDePagamento: this.fb.control("", Validators.required),
					valor: this.fb.control(0, Validators.required),
				}),
			]),
		});
	}

	getSaldoContaCorrente(): void {
		this.caixaAbertoService
			.getContaCorrente(this.form.controls["responsavel"].value.codigo)
			.subscribe((response) => {
				this.saldoCC = response;
			});
	}

	getFormasDePagamento(): void {
		this.caixaAbertoService.formasPagamento().subscribe((response) => {
			this.formasDePagamento = response.map((v) => {
				const captalizePipe = new CaptalizePipe();
				v.descricao = captalizePipe.transform(v.descricao);
				// logica pagamento credito offline e online
				if (v.tipoFormaPagamento === "CA" && v.receberValorTotal === true) {
					v.descricao = v.descricao + " - ONLINE";
					v.tipoFormaPagamento = FormPagamentoEnum.CARTAOCREDITO_ONLINE;
				}
				if (v.tipoFormaPagamento === "CA" && v.receberValorTotal === false) {
					v.descricao = v.descricao + " - OFFLINE";
					v.tipoFormaPagamento = FormPagamentoEnum.CARTAOCREDITO_OFFLINE;
				}
				return { descricao: v.descricao, value: v, disabled: false };
			});
			this.formasDePagamentoApresentar = this.formasDePagamento;
			this.cd.detectChanges();
		});
	}

	public adicionarFormaDePagamento(): void {
		this.formasDePagamentoForm.push(
			this.fb.group({
				formaDePagamento: this.fb.control(0, Validators.required),
				valor: this.fb.control(0, Validators.required),
			})
		);
	}

	public deletarFormaDePagamento(index: number): void {
		if (
			this.formasDePagamentoForm.value[index].formaDePagamento
				.tipoFormaPagamento === FormPagamentoEnum.CREDITOCONTACORRENTE
		) {
			this.removerCreditoCC();
		}
		this.formasDePagamentoForm.removeAt(index);
	}

	editar() {
		this.dialogRef = this.dialogService.open(this.editDialog, {
			disableClose: true,
			id: "editar",
			autoFocus: false,
			width: "800px",
			panelClass: "editar",
		});
		this.dialogRef.beforeClosed().subscribe((response) => {
			if (!this.permissaoService.temPermissaoAdm("3.06")) {
				this.dialogSemPermissao();
			}
		});
	}

	validar() {
		if (!this.pagamentosSelecionadosEValidos) {
			let temErro = false;
			this.formasDePagamentoForm.getRawValue().forEach((pag) => {
				if (pag.formaDePagamento.descricao) {
					if (!pag.valor || pag.valor === 0) {
						this.snotifyService.error(
							"A forma de pagamento " +
							pag.formaDePagamento.descricao +
							" não tem valor informado"
						);
						temErro = true;
					} else if (!pag.valid) {
						this.snotifyService.error(
							"A forma de pagamento " +
							pag.formaDePagamento.descricao +
							" está inválida"
						);
						temErro = true;
					}
				}
			});
			if (temErro) {
				return;
			}
		}

		if (this.valorSobressalenteResidual < 0) {
			console.log(this.valorSobressalenteResidual);
			this.snotifyService.error(
				"Ainda falta lançar um valor de " +
					this.formatarValor(this.valorSobressalenteResidual * -1)
			);
			return;
		}

		if (this.valorSobressalenteResidual > 0) {
			const dialogRef = this.pactoModal.open(
				"Atenção",
				ModalConfirmarRecebimentoComponent,
				PactoModalSize.MEDIUM
			);
			dialogRef.componentInstance.dados = {
				msg:
					"Você está efetuando um pagamento que vai gerar um crédito de " +
					this.formatarValor(this.valorSobressalenteResidual) +
					" para o cliente. Confirmar?",
			};
			dialogRef.componentInstance.update.subscribe((respModal) => {
				if (respModal === "SIM") {
					this.autorizarVenda();
				}
			});
			return;
		}

		this.autorizarVenda();
	}

	autorizarVenda(): void {
		if (
			this.sessionService.loggedUser.pedirSenhaFuncionalidade &&
			this.sessionService.loggedUser.pedirSenhaFuncionalidade === true
		) {
			const modalConfirmacao: any = this.dialogService.open(
				DialogAutorizacaoAcessoComponent,
				{
					disableClose: true,
					id: "autorizacao-acesso",
					autoFocus: false,
				}
			);
			modalConfirmacao.componentInstance.form
				.get("usuario")
				.setValue(this.sessionService.loggedUser.username);
			modalConfirmacao.componentInstance.confirm.subscribe((result) => {
				modalConfirmacao.componentInstance.disable();
				this.autorizarAcessoService
					.validarPermissao(
						this.sessionService.chave,
						result.data.usuario,
						result.data.senha,
						"MovPagamento",
						"4.07 - Movimentos de pagamento",
						this.sessionService.empresaId
					)
					.subscribe(
						(response: any) => {
							this.receber();
							result.modal.close();
						},
						(error) => {
							this.cd.detectChanges();
							this.snotifyService.error(error.error.meta.message);
						}
					);
			});
		} else {
			this.autorizarAcessoService
				.validarPermissaoUsuarioLogado(
					this.sessionService.chave,
					this.sessionService.codUsuarioZW,
					this.sessionService.empresaId,
					"MovPagamento",
					"4.07 - Movimentos de pagamento"
				)
				.subscribe(
					(response) => {
						this.receber();
					},
					(httpResponseError) => {
						this.snotifyService.error(httpResponseError.error.meta.message);
					}
				);
		}
	}

	receber() {
		this.loaderService.initForce();

		const body: BodyPagamento = {
			pessoa: this.form.controls["responsavel"].value.codigo,
			tipoComprador: this.form.controls["responsavel"].value.tipoComprador,
			nomeComprador: this.form.controls["responsavel"].value.nome,
			dataPagamento: moment(
				this.form.controls["dataDeRecebimento"].value
			).valueOf(),
			usuario: this.sessionService.loggedUser.id,
			observacao: this.form.controls["observacao"].value,
			empresa: this.caixaAbertoService.empresaSelecionada,
			parcelas: this.caixaAbertoService.parcelas.map((v) => ({
				codigo: v.codigo,
			})),
			valor: this.totalLancado,
			valorParcelas: this.valorTotalParcelas,
			valorSobressalenteResidual: this.valorSobressalenteResidual,
			valorContaCorrente: this.saldoCC,
			valorDebitoContaCorrente: this.valorDebitoContaCorrente,
			valorCreditoContaCorrente: this.valorCreditoContaCorrente,
			valorMultaJuros: this.valorMulta,
			cobrarMultaJuros: this.form.controls["cobrarMultaJuros"].value,
		};

		// fluxo pagamento com tag existeTagReceberValorTotal
		if (this.existeTagReceberValorTotal) {
			const formValueCampoEspecial = this.formasDePagamentoForm.value[0];
			const itemType: "pix" | "cartao" =
				formValueCampoEspecial.formaDePagamento.tipoFormaPagamento ===
				FormPagamentoEnum.PIX
					? "pix"
					: "cartao";
			const campoEspecialCorrigido = {
				[itemType]: {
					formaPagamento: formValueCampoEspecial.formaDePagamento.codigo,
					convenioCobranca: formValueCampoEspecial.convenioCobranca,
					nrParcelas: formValueCampoEspecial.nrParcelas,
					tokenAragorn: formValueCampoEspecial.tokenAragorn,
					valor: formValueCampoEspecial.valor,
					numero: formValueCampoEspecial.numero,
					mesValidade: formValueCampoEspecial.validade
						? parseInt(formValueCampoEspecial.validade.split("/")[0])
						: undefined,
					anoValidade: formValueCampoEspecial.validade
						? parseInt(formValueCampoEspecial.validade.split("/")[1])
						: undefined,
					cvv: formValueCampoEspecial.cvv,
					titular: formValueCampoEspecial.titular,
					documento: formValueCampoEspecial.documento,
					tipoParcelamento: formValueCampoEspecial.tipoParcelamento,
					operadoraCartao: formValueCampoEspecial.operadoraCartao,
				},
			};
			if (formValueCampoEspecial.tokenAragorn) {
				delete campoEspecialCorrigido["numero"];
				delete campoEspecialCorrigido["mesValidade"];
				delete campoEspecialCorrigido["anoValidade"];
				delete campoEspecialCorrigido["cvv"];
				delete campoEspecialCorrigido["titular"];
				delete campoEspecialCorrigido["documento"];
			}

			this.caixaAbertoService
				.receberPagamentoComTagFull(
					body,
					campoEspecialCorrigido,
					itemType === "cartao" ? "transacao" : itemType
				)
				.subscribe(
					(connected) => {
						if (!!connected.reciboPagamento) {
							this.notificar(
								"CAIXA_EM_ABERTO_V2_RECEBER_SUCESSO_CARTAO_ONLINE"
							);
							this.pagamentoResponse.status = "SUCESSO";
							this.pagamentoResponse.data = connected.situacaoDescricao;
							this.pagamentoResponse.recibo = {
								codigo: connected.reciboPagamento,
							};
							this.finalizarPagamento();
						} else if (itemType === "pix") {
							this.pixData = connected;
							this.loaderService.stopForce();
							this.cd.detectChanges();
						} else {
							this.pagamentoResponse.status = "ERRO";
							this.pagamentoResponse.data = "adquirente";
							this.pagamentoResponse.msg = connected.retorno;
							this.snotifyService.error(connected.retorno);
							this.finalizarPagamento();
						}
					},
					(error) => {
						this.pagamentoResponse.status = "ERRO";
						this.pagamentoResponse.data = error.error.meta.error.startsWith(
							"validacao_"
						)
							? "requisicao"
							: "adquirente";
						this.pagamentoResponse.msg = error.error.meta.message;
						this.snotifyService.error(error.error.meta.message);
						this.finalizarPagamento();
					}
				);
		} else {
			const pagamentosConvencional = this.formasDePagamentoForm
				.getRawValue()
				.filter(v => v.formaDePagamento.codigo) // filtra apenas os válidos
				.map((v) => {
					v.formaPagamento = v.formaDePagamento.codigo;
					if (v.cheques) {
						v.cheques.every(
							(cheque) =>
								(cheque.dataCompensacao = moment(
									cheque.dataCompensacao
								).valueOf())
						);
					}
					v.dataCredito = v.dataCredito
						? moment(v.dataCredito).valueOf()
						: null;
					v.dataQuitacao = v.dataQuitacao
						? moment(v.dataQuitacao).valueOf()
						: null;
					v.convenioCobranca =
						v.convenioCobranca && v.convenioCobranca.codigo
							? v.convenioCobranca.codigo
							: null;
					delete v.formaDePagamento;
					return v;
				});
			// fluxo convencional
			this.caixaAbertoService
				.receberPagamento(body, pagamentosConvencional)
				.subscribe(
					(sucess) => {
						this.notificar("CAIXA_EM_ABERTO_V2_RECEBER_SUCESSO");
						this.pagamentoResponse.status = "SUCESSO";
						this.pagamentoResponse.data = sucess;
						this.pagamentoResponse.recibo = sucess;
						this.finalizarPagamento();
					},
					(error) => {
						this.pagamentoResponse.status = "ERRO";
						this.pagamentoResponse.msg =
							error.error.meta.message || error.message;
						// this.pagamentoResponse.data =
						// 	error.error.meta.error === "erro_pagar"
						// 		? "requisicao"
						// 		: "adquirente"; //tem que melhorar a forma de mapear de acordo com tipo de erro 'requisicao' OU 'adquirente'
						this.snotifyService.error(error.error.meta.message);
						this.finalizarPagamento();
					}
				);
		}
	}

	public get quantidadeDeItensSelecionados(): number {
		if (this.caixaAbertoService.parcelas) {
			return Object.values(this.caixaAbertoService.parcelas).length;
		} else {
			return 0;
		}
	}

	public get formasDePagamentoForm(): FormArray {
		return this.form.get("formasDePagamento") as FormArray;
	}

	public get existeTagReceberValorTotal(): Array<any> {
		return this.formasDePagamentoForm.value
			.map((pagamento) => pagamento.formaDePagamento)
			.find((p) => p.receberValorTotal);
	}

	public get pagamentosSelecionadosEValidos(): boolean {
		return (
			this.formasDePagamentoForm.valid &&
			!this.formasDePagamentoForm.value.some(
				(pag) => pag.formaDePagamento === 0
			)
		);
	}

	public get valorTotalParcelas(): number {
		return parseFloat(
			this.parcelas
				.map((parcela) => parcela.valorParcela)
				.reduce((a, b) => a + b, 0)
				.toFixed(2)
		);
	}

	public get valorTotalReceber(): number {
		return parseFloat((
			this.valorTotalParcelas +
			(this.vaiCobrarMultaJuros ? this.valorMulta : 0) +
			(this.vaiUsarDebitoContaCorrente
				? this.valorDebitoContaCorrente < 0
					? this.valorDebitoContaCorrente * -1
					: 0
				: 0) -
			(this.vaiUsarCreditoContaCorrente ? this.valorCreditoContaCorrente : 0)
		).toFixed(2));
	}

	public get valorDebitoContaCorrente(): number {
		return this.form.get("valorDebitoContaCorrente").value
			? this.form.get("valorDebitoContaCorrente").value
			: 0;
	}

	public get valorCreditoContaCorrente(): number {
		return this.form.get("valorCreditoContaCorrente").value
			? this.form.get("valorCreditoContaCorrente").value
			: 0;
	}

	public get valorTotal(): number {
		return parseFloat(
			this.parcelas
				.map(
					(parcela) =>
						parcela.valorParcela +
						(this.form.get("cobrarMultaJuros").value
							? parcela.valorMulta + parcela.valorJuros
							: 0)
				)
				.reduce((a, b) => a + b, 0)
				.toFixed(2)
		);
	}

	public get valorMulta(): number {
		return parseFloat(
			this.parcelas
				.map((parcela) => parcela.valorMulta + parcela.valorJuros)
				.reduce((a, b) => a + b, 0)
				.toFixed(2)
		);
	}

	get valorSobressalenteResidual(): number {
		return this.totalLancado - this.valorTotalReceber;
	}

	get valorSobressalenteResidualApresentar(): number {
		if (this.valorSobressalenteResidual < 0) {
			return this.valorSobressalenteResidual * -1;
		} else {
			return this.valorSobressalenteResidual;
		}
	}

	get totalLancado(): number {
		return parseFloat(
			this.formasDePagamentoForm
				.getRawValue()
				.map((form) => ((form.valor || 0) < 0 ? 0 : form.valor || 0))
				.reduce((a, b) => a + b, 0)
				.toFixed(2)
		);
	}

	get quantoFalta(): number {
		const falta = this.valorSobressalenteResidual;
		return parseFloat((falta < 0 ? falta * -1 : falta).toFixed(2));
	}

	get formPagamentoEnum() {
		return FormPagamentoEnum;
	}

	finalizarPagamentoPix(resp: any) {
		this.pagamentoResponse.status = "SUCESSO";
		this.pagamentoResponse.recibo = {
			codigo: resp.reciboPagamento,
		};
		this.notificar("CAIXA_EM_ABERTO_V2_RECEBER_SUCESSO_PIX");
		this.finalizarPagamento();
	}

	finalizarPagamento() {
		this.loaderService.stopForce();
		this.dialogRef = this.dialogService.open(this.finalizarDialog, {
			disableClose: true,
			id: "finalizar",
			autoFocus: false,
			width: "800px",
			panelClass: "finalizar",
		});
		this.dialogRef.afterClosed().subscribe((response) => {
			if (response === "voltar") {
				this.router.navigate(["adm", "caixa-em-aberto"]);
			}
		});
	}

	permissaoCCHandler(temPermissao: boolean) {
		if (!temPermissao) {
			this.formasDePagamentoForm.removeAt(
				this.formasDePagamentoForm.controls.findIndex(
					(v) =>
						v.value.formaDePagamento.tipoFormaPagamento ===
						FormPagamentoEnum.CREDITOCONTACORRENTE
				)
			);
		}
	}

	dialogSemPermissao() {
		const modalConfirmacao: any = this.dialogService.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form.get("usuario").setValue("");
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"DataBase",
					"3.06 - Liberação de Data Base",
					this.sessionService.empresaId.toString()
				)
				.subscribe({
					next: () => {
						modalConfirmacao.close();
					},
					error: (error) => {
						this.snotifyService.error(error.error.meta.message);
						this.form.get("dataDeRecebimento").setValue(moment().valueOf());
						this.cd.detectChanges();
						modalConfirmacao.close();
					},
				});
		});
	}

	removerDebitoCC() {
		this.form.get("valorDebitoContaCorrente").setValue(0.0);
	}

	inserirDebitoCC() {
		this.form.get("valorDebitoContaCorrente").setValue(this.saldoCC);
		this.snotifyService.clear();
		this.snotifyService.info(
			"Foi adicionado ao total a receber o valor de " +
				this.formatarValor(this.saldoCC * -1)
		);
	}

	removerCreditoCC() {
		this.form.get("valorCreditoContaCorrente").setValue(0.0);
	}

	inserirCreditoCC() {
		this.form.get("valorCreditoContaCorrente").setValue(0.0);
		let valorUsarContaCorrente = 0;
		const valorResudual = this.valorSobressalenteResidual * -1;
		if (valorResudual <= this.saldoCC) {
			valorUsarContaCorrente = valorResudual;
		} else {
			valorUsarContaCorrente = this.saldoCC;
		}
		this.form.get("valorCreditoContaCorrente").setValue(valorUsarContaCorrente);
		this.snotifyService.clear();
		this.snotifyService.info(
			"Foi removido do total a receber o valor de " +
				this.formatarValor(valorUsarContaCorrente) +
				", que será utilizado do valor que o cliente tem na conta corrente."
		);
	}

	imprimirRecibo(codRecibo) {
		this.telaClienteService
			.imprimirRecibo(
				this.sessionService.chave,
				codRecibo,
				this.sessionService.loggedUser.id
			)
			.subscribe(
				(response) => {
					window.open(response.content, "_blank");
				},
				(error) => this.snotifyService.error("Erro ao gerar recibo!")
			);
	}

	cliente() {
		const resp = this.form.get("responsavel").value;
		if (resp && resp.matricula && resp.matricula.length > 0) {
			return true;
		} else {
			return false;
		}
	}

	irTelaAluno() {
		this.dialogRef.close();
		const resp = this.form.get("responsavel").value;
		this.router.navigateByUrl("/pessoas/perfil-v2/" + resp.matricula);
	}

	formatarValor(valor) {
		return this.currencyPipe.transform(valor, "BRL", "symbol", "1.2-2");
	}

	public get vaiCobrarMultaJuros(): boolean {
		return this.form.controls["cobrarMultaJuros"].value;
	}

	public get vaiUsarDebitoContaCorrente(): boolean {
		return this.form.controls["debitoContaCorrente"].value;
	}

	public get vaiUsarCreditoContaCorrente(): boolean {
		return this.form.controls["creditoContaCorrente"].value;
	}

	public get vaiUsarPix(): boolean {
		return (
			this.formasDePagamentoForm.value[0].formaDePagamento
				.tipoFormaPagamento === FormPagamentoEnum.PIX
		);
	}

	voltarNavegacao(): void {
		if (this.pessoas && this.pessoas[0].label) {
			this.router.navigate([
				"adm",
				"caixa-em-aberto",
				"lista",
				this.pessoas[0].label,
			]);
		} else {
			this.router.navigate(["adm", "caixa-em-aberto"]);
		}
	}

	get hoje() {
		return new Date();
	}

	enviarReciboEmail(): void {
		this.dialogRef.close("enviar_email");
		if (!this.emails || this.emails.length === 0) {
			const responsavel = this.form.get("responsavel").value;
			if (responsavel.emails && responsavel.emails.length > 0) {
				responsavel.emails.map((email) => {
					this.emails.push(email.email);
				});
			}
		}

		this.dialogRef = this.dialogService.open(this.reciboEmailDialog, {
			disableClose: true,
			id: "envioEmail",
			autoFocus: false,
			width: "800px",
			panelClass: "envioEmail",
		});
		this.dialogRef.beforeClosed().subscribe((response) => {
			this.finalizarPagamento();
		});
	}

	public incluirEmail(): void {
		this.formEmail.markAsTouched();
		if (this.formEmail.invalid) {
			return;
		}
		const emailValue = this.formEmail.get("email").value;
		this.emails.push(emailValue);
		this.formEmail.reset();
	}

	public excluirEmail(index): void {
		this.emails.splice(index, 1);
	}

	public cancelarEnviarEmail(): void {
		this.dialogRef.close("fechar");
	}

	public enviarEmail(): void {
		this.telaClienteService
			.enviarReciboEmail(
				this.sessionService.chave,
				this.pagamentoResponse.recibo.codigo,
				this.sessionService.codUsuarioZW,
				this.emails
			)
			.subscribe(
				(res) => {
					if (res.content) {
						this.snotifyService.success(res.content);
						this.dialogRef.close("fechar");
					} else {
						this.snotifyService.error(res.meta.message);
					}
				},
				({ error }) => {
					this.snotifyService.error(error.meta.message);
				}
			);
	}

	permiteReceberPagamento(): any {
		const permition = this.permissao4_07;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL
			);
		const retor = isPermited !== undefined && isPermited;
		return retor ? true : false;
	}

	private notificar(string: string) {
		try {
			this.sessionService.notificarRecursoEmpresa(string);
		} catch (e) {
			console.error(e);
		}
	}
}

export enum FormPagamentoEnum {
	DINHEIRO = "AV",
	CHEQUE = "CH",
	CARTAOCREDITO_ONLINE = "CA_ON",
	CARTAOCREDITO_OFFLINE = "CA_OFF",
	CARTAODEBITO = "CD",
	CONVENIO = "CO",
	CREDITOCONTACORRENTE = "CC",
	BOLETOBANCARIO = "BB",
	PAGAMENTODIGITAL = "PD",
	LOTE = "LO",
	PARCEIRO_FIDELIDADE = "PF",
	TRANSFERENCIA_BANCARIA = "TB",
	PIX = "PX",
}
