<pacto-cat-layout-v2>
	<div class="content-wrapper">
		<div class="icon-voltar">
			<div class="type-h3-bold">Ranking</div>
		</div>
		<div class="layout-subtitle">
			<div style="margin-left: auto">
				<button
					(click)="atualizarHandler()"
					class="btn pacto-primary"
					id="refresh-ranking"
					title="Clique para atualizar com dados recentes">
					<i class="pct pct-refresh-cw"></i>
					<span class="btn-share-label">Atualizar</span>
				</button>
				<div class="update-indicadores">
					<span *ngIf="lastUpdate">
						<ng-container i18n="treino-bi:ultima-atualizacao">
							Última atualização:
						</ng-container>
						{{ lastUpdate }}
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="table-wrapper pacto-shadow">
		<div class="titulo">
			Ranking de professores
			<div class="acoes">
				<div
					#dropDownPeriodo
					[autoClose]="'outside'"
					[placement]="'bottom-right'"
					class="d-inline-block"
					ngbDropdown>
					<button
						class="btn pacto-primary"
						id="periodo"
						ngbDropdownToggle
						title="Defina a data inicial e final do Ranking">
						<i class="pct pct-calendar"></i>
						<span class="icon-drop">
							<i class="pct pct-chevron-up"></i>
							<i class="pct pct-chevron-down"></i>
						</span>
					</button>
					<div
						aria-labelledby="periodo"
						class="exportar-dropdown"
						ngbDropdownMenu>
						<div id="content-dates">
							<label>Período de pontuação</label>
							<pacto-datepicker
								[control]="formGroup.get('dataInicio')"></pacto-datepicker>
							<label>até</label>
							<pacto-datepicker
								[control]="formGroup.get('dataFim')"></pacto-datepicker>
							<div>
								<button
									(click)="atualizar()"
									class="btn pacto-primary"
									id="refresh-ranking-dates">
									<i class="pct pct-search"></i>
									<span class="btn-share-label">Buscar</span>
								</button>
							</div>
						</div>
					</div>
				</div>

				<button
					*ngIf="permissaoAcessarConfiguracaoRanking"
					(click)="configurarRankingProfessores()"
					class="btn pacto-primary"
					id="configurar-ranking"
					title="Configure os indicadores da sua empresa">
					<i class="pct pct-settings"></i>
					<span class="btn-share-label">Configurar ranking</span>
				</button>

				<pacto-share-button
					*ngIf="filterConfig"
					[columns]="obterColunasRelatorio()"
					[endpoint]="table?.endpointUrl"
					[filterConfig]="filterConfig"
					[filtros]="getFiltersShare()"
					[sessionService]="sessionService"
					[titulo]="'Ranking de professores'"
					[total]="1000"
					telaId="ranking"></pacto-share-button>

				<div
					#filterDropdown="ngbDropdown"
					class="filter filtros-ranking d-inline-block"
					ngbDropdown>
					<pacto-filter
						#filtro
						(filterChange)="onSearch($event)"
						*ngIf="filterConfig"
						[filterConfig]="filterConfig"></pacto-filter>
				</div>
			</div>
		</div>
		<pacto-podium
			#podium
			(clickLugar)="abrirDetalhes($event)"
			[fim]="fim"
			[inicio]="inicio"></pacto-podium>
		<div class="periodo-pontuacao">
			Pontuação no período : {{ inicio | date : "dd/MM/yyyy" }} -
			{{ fim | date : "dd/MM/yyyy" }}
		</div>
		<div class="titulo linha">Ranking geral</div>
		<div class="rolagem-tabela">
			<pacto-relatorio
				#relatorio
				(iconClick)="actionClickHandler($event)"
				(loadedData)="getLastUpdate()"
				*ngIf="ready"
				[actionTitulo]="'Ações'"
				[enableZebraStyle]="true"
				[filterConfig]="filterConfig"
				[showShare]="false"
				[table]="table"
				class="ranking-profs"></pacto-relatorio>
		</div>
	</div>
</pacto-cat-layout-v2>
<ng-template #nomeColumn let-item="item">
	<img class="foto-professor" src="{{ item?.urlFoto }}" />
	<span class="text-capitalize">{{ item?.nome }}</span>
</ng-template>
<ng-template #totalColumn let-item="item">
	<span>{{ item.total | number : "1.2-2" : "en-US" }}</span>
</ng-template>
<ng-template #desempenho let-item="item">
	<span class="desempenho">
		<i *ngIf="item?.desempenho === 'draw'" class="pct pct-minus"></i>
		<i *ngIf="item?.desempenho === 'up'" class="pct pct-caret-down"></i>
		<i *ngIf="item?.desempenho === 'down'" class="pct pct-caret-up"></i>
	</span>
</ng-template>
<ng-template
	#configurarRanking
	i18n="@@relatorio-ranking-professores:configurar-ranking">
	Configurar ranking
</ng-template>

<!-- TABLE LABELS -->
<ng-template #tableTitulo i18n="@@relatorio-ranking-professores:titulo">
	Ranking de professores
</ng-template>
<ng-template #nomeTitulo i18n="@@relatorio-ranking-professores:nome:coluna">
	Nome
</ng-template>
<ng-template
	#totalAlunosTitulo
	i18n="@@relatorio-ranking-professores:total-alunos:indicador">
	Total de Alunos
</ng-template>
<ng-template
	#treinosVencendoTitulo
	i18n="@@relatorio-ranking-professores:treinos-vencendo:indicador">
	Treinos vencendo
</ng-template>
<ng-template
	#trocaramDeCarteiraTitulo
	i18n="@@relatorio-ranking-professores:trocaram-carteira:indicador">
	Trocaram de carteira
</ng-template>
<ng-template
	#porcentagemCrescimentoTitulo
	i18n="@@relatorio-ranking-professores:perc-crescimento-carteira:indicador">
	% crescimento carteira
</ng-template>
<ng-template
	#percRenovaramContratoTitulo
	i18n="@@relatorio-ranking-professores:perc-renovacao-contrato:indicador">
	% renovação de contrato
</ng-template>
<ng-template
	#treinoEmDiaTitulo
	i18n="@@relatorio-ranking-professores:perc-treinos-dia:indicador">
	% treinos em dia
</ng-template>
<ng-template
	#treinoVencidoTitulo
	i18n="@@relatorio-ranking-professores:perc-treinos-vencido:indicador">
	% treinos vencidos
</ng-template>
<ng-template
	#agAguardandoConfTitulo
	i18n="
		@@relatorio-ranking-professores:agenda-aguardando-confirmacao:indicador">
	Agenda: Aguardando confirmação
</ng-template>
<ng-template
	#agCancelaramTitulo
	i18n="@@relatorio-ranking-professores:agenda-alunos-cancelaram:indicador">
	Agenda: Alunos que cancelaram
</ng-template>
<ng-template
	#agConfirmaramTitulo
	i18n="@@relatorio-ranking-professores:agenda-alunos-conf-presenca:indicador">
	Agenda: Alunos que confirmaram presença
</ng-template>
<ng-template
	#agFaltaramTitulo
	i18n="@@relatorio-ranking-professores:agenda-alunos-faltaram:indicador">
	Agenda: Alunos que faltaram
</ng-template>
<ng-template
	#agReagendaramTitulo
	i18n="@@relatorio-ranking-professores:agenda-alunos-reagendaram:indicador">
	Agenda: Alunos que reagendaram
</ng-template>
<ng-template
	#agAvaliacoesRealizadasTitulo
	i18n="
		@@relatorio-ranking-professores:agenda-avaliacoes-fisicas-realizadas:indicador">
	Agenda: Av. físicas que foram realizadas
</ng-template>
<ng-template
	#agCompareceramTitulo
	i18n="@@relatorio-ranking-professores:agenda-compareceram:indicador">
	Agenda: Compareceram
</ng-template>
<ng-template
	#agNovosTreinoTitulo
	i18n="@@relatorio-ranking-professores:agenda-novos-treinos:indicador">
	Agenda: Novos treinos
</ng-template>
<ng-template
	#agOcupacaoTitulo
	i18n="@@relatorio-ranking-professores:agenda-ocupacao:indicador">
	Agenda: Ocupação
</ng-template>
<ng-template
	#agTreinosRenovadoTitulo
	i18n="@@relatorio-ranking-professores:agenda-treinos-renovados:indicador">
	Agenda: Treinos que foram renovados
</ng-template>
<ng-template
	#agTreinosRevisadoTitulo
	i18n="@@relatorio-ranking-professores:agenda-treinos-revisados:indicador">
	Agenda: Treinos que foram revisados
</ng-template>
<ng-template
	#agendamentosTitulo
	i18n="@@relatorio-ranking-professores:agendamentos:indicador">
	Agendamentos
</ng-template>
<ng-template
	#alunosAtivosTitulo
	i18n="@@relatorio-ranking-professores:alunos-ativos:indicador">
	Alunos Ativos
</ng-template>
<ng-template
	#alunosNaoUtilizaAppTitulo
	i18n="@@relatorio-ranking-professores:alunos-ativos-nao-usa-app:indicador">
	Alunos ativos que não utilizam o App
</ng-template>
<ng-template
	#alunosAtivosUtilizaAppTitulo
	i18n="@@relatorio-ranking-professores:alunos-ativos-usa-app:indicador">
	Alunos ativos que utilizam o App
</ng-template>
<ng-template
	#alunosCanceladosTitulo
	i18n="@@relatorio-ranking-professores:alunos-cancelados:indicador">
	Alunos cancelados
</ng-template>
<ng-template
	#alunosInativosTitulo
	i18n="@@relatorio-ranking-professores:alunos-inativos:indicador">
	Alunos Inativos
</ng-template>
<ng-template
	#alunosInativosUtilizaAppTitulo
	i18n="@@relatorio-ranking-professores:alunos-inativos-usa-app:indicador">
	Alunos inativos que utilizam o App
</ng-template>
<ng-template
	#alunosNaoRenovaramTitulo
	i18n="
		@@relatorio-ranking-professores:alunos-nao-renovaram-contrato:indicador">
	Alunos que não renovaram contrato
</ng-template>
<ng-template
	#estrela1Titulo
	i18n="@@relatorio-ranking-professores:avaliacoes-1-estrela:indicador">
	Avaliações com 1 estrela
</ng-template>
<ng-template
	#estrela2Titulo
	i18n="@@relatorio-ranking-professores:avaliacoes-2-estrela:indicador">
	Avaliações com 2 estrela
</ng-template>
<ng-template
	#estrela3Titulo
	i18n="@@relatorio-ranking-professores:avaliacoes-3-estrela:indicador">
	Avaliações com 3 estrela
</ng-template>
<ng-template
	#estrela4Titulo
	i18n="@@relatorio-ranking-professores:avaliacoes-4-estrela:indicador">
	Avaliações com 4 estrela
</ng-template>
<ng-template
	#estrela5Titulo
	i18n="@@relatorio-ranking-professores:avaliacoes-5-estrela:indicador">
	Avaliações com 5 estrela
</ng-template>
<ng-template
	#avaliacaoPeloAppTitulo
	i18n="@@relatorio-ranking-professores:avaliacoes-pelo-app:indicador">
	Avaliações pelo aplicativo
</ng-template>
<ng-template
	#comAvaliacaoFisicaTitulo
	i18n="@@relatorio-ranking-professores:com-avaliacao-fisica:indicador">
	Com Avaliação Física
</ng-template>
<ng-template
	#comTreinoTitulo
	i18n="@@relatorio-ranking-professores:com-treino:indicador">
	Com treino
</ng-template>
<ng-template
	#contratoAVencerTitulo
	i18n="@@relatorio-ranking-professores:contratos-a-vencer:indicador">
	Contratos a vencer
</ng-template>
<ng-template
	#contratoVencidoTitulo
	i18n="@@relatorio-ranking-professores:contratos-vencidos:indicador">
	Contratos vencidos
</ng-template>
<ng-template
	#agDisponibilidadeTitulo
	i18n="@@relatorio-ranking-professores:disponibilidade:indicador">
	Disponibilidade
</ng-template>
<ng-template
	#entraramCarteiraTitulo
	i18n="@@relatorio-ranking-professores:entraram-na-carteira:indicador">
	Entraram na carteira
</ng-template>
<ng-template
	#agHorasAtendimentoTitulo
	i18n="@@relatorio-ranking-professores:horas-atendimento-agenda:indicador">
	Horas atendimento na agenda
</ng-template>
<ng-template
	#agHorasDisponibilidadeTitulo
	i18n="@@relatorio-ranking-professores:horas-disponibilidade-agenda:indicador">
	Horas disponibilidade na agenda
</ng-template>
<ng-template
	#mediaProgramaTitulo
	i18n="@@relatorio-ranking-professores:media-programa-treino:indicador">
	Média do programa de treino (meses)
</ng-template>
<ng-template
	#mediaCarteiraTitulo
	i18n="@@relatorio-ranking-professores:media-carteira:indicador">
	Média na carteira (meses)
</ng-template>
<ng-template
	#novosAlunosTitulo
	i18n="@@relatorio-ranking-professores:novos-alunos-carteira:indicador">
	Novos alunos na carteira
</ng-template>
<ng-template
	#alunosDoTreinoAcessaramTreinoTitulo
	i18n="
		@@relatorio-ranking-professores:numero-alunos-acessaram-treino:indicador">
	Nr. alunos do Treino que acessaram
</ng-template>
<ng-template
	#acessaramTreinoTitulo
	i18n="@@relatorio-ranking-professores:numero-alunos-acessaram:indicador">
	Nr. alunos que acessaram
</ng-template>
<ng-template
	#execucoesTreinoTitulo
	i18n="@@relatorio-ranking-professores:numero-execucoes-treino:indicador">
	Nr. de execuções de Treino
</ng-template>
<ng-template
	#execucoesTreinoPeloAppTitulo
	i18n="@@relatorio-ranking-professores:numero-execucoes-treino-app:indicador">
	Nr. de execuções de Treino pelo aplicativo
</ng-template>
<ng-template
	#programaEmDiaTitulo
	i18n="@@relatorio-ranking-professores:programa-em-dia:indicador">
	Programa em dia
</ng-template>
<ng-template
	#renovaramContratoTitulo
	i18n="@@relatorio-ranking-professores:renovaram-contrato:indicador">
	Renovaram contrato
</ng-template>
<ng-template
	#sairamCarteiraTitulo
	i18n="@@relatorio-ranking-professores:sairam-carteira:indicador">
	Saíram da carteira
</ng-template>
<ng-template
	#semAvaliacaoFisicaTitulo
	i18n="@@relatorio-ranking-professores:sem-avaliacao-fisica:indicador">
	Sem Avaliação Física
</ng-template>
<ng-template
	#alunosAtivosSemTreinoTitulo
	i18n="@@relatorio-ranking-professores:sem-treino:indicador">
	Sem treino
</ng-template>
<ng-template #totalTitulo i18n="@@relatorio-ranking-professores:total:coluna">
	Total
</ng-template>
<ng-template
	#acompanhadosTitulo
	i18n="@@relatorio-ranking-professores:acompanhados:indicador">
	Acompanhados
</ng-template>
<ng-template
	#atividadesAcompanhadasTitulo
	i18n="@@relatorio-ranking-professores:atividades-acompanhadas:indicador">
	Atividades Acompanhadas
</ng-template>

<!-- FILTER LABELS -->
<ng-template
	#mesAnoLabel
	i18n="@@relatorio-ranking-professores:data-inicio:filtro">
	Mês/Ano
</ng-template>
<ng-template
	#professorLabel
	i18n="@@relatorio-ranking-professores:professores:filtro">
	Professores
</ng-template>
<ng-template #modalidadeLabel>
	<span i18n="@@relatorio-ranking-professores:modalidade">Modalidade</span>
</ng-template>
<ng-template #personalLabel>
	<span i18n="@@relatorio-ranking-professores:personal">Personal</span>
</ng-template>
<ng-template #situacaoLabel>
	<span i18n="@@relatorio-ranking-professores:situacao">Situação</span>
</ng-template>
<ng-template #cargaHorariaLabel>Carga Horária</ng-template>
<ng-template #filterTranslator let-item="name">
	<ng-container [ngSwitch]="item">
		<span
			*ngSwitchCase="'naoincluir'"
			i18n="@@relatorio-ranking-professores:naoincluir">
			Não incluir personal
		</span>
		<span
			*ngSwitchCase="'2'"
			i18n="@@relatorio-ranking-professores:carga-horaria-2">
			2 horas
		</span>
		<span
			*ngSwitchCase="'4'"
			i18n="@@relatorio-ranking-professores:carga-horaria-4">
			4 horas
		</span>
		<span
			*ngSwitchCase="'6'"
			i18n="@@relatorio-ranking-professores:carga-horaria-6">
			6 horas
		</span>
		<span
			*ngSwitchCase="'8'"
			i18n="@@relatorio-ranking-professores:carga-horaria-8">
			8 horas
		</span>
		<span
			*ngSwitchCase="'12'"
			i18n="@@relatorio-ranking-professores:carga-horaria-12">
			12 horas
		</span>
		<span
			*ngSwitchCase="'interno'"
			i18n="@@relatorio-ranking-professores:interno">
			Interno
		</span>
		<span
			*ngSwitchCase="'externo'"
			i18n="@@relatorio-ranking-professores:externo">
			Externo
		</span>
		<span *ngSwitchCase="'ativo'" i18n="@@relatorio-ranking-professores:ativo">
			Ativo
		</span>
		<span
			*ngSwitchCase="'inativo'"
			i18n="@@relatorio-ranking-professores:inativo">
			Inativo
		</span>
	</ng-container>
</ng-template>
