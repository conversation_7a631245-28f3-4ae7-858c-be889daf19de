import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import {
	ConfiguracaoRankingModel,
	TreinoApiConfiguracaoRankingService,
	TreinoApiProfessorService,
} from "treino-api";
import { ColunasRankingProfessor } from "../colunasRankingProfessor.model";

@Component({
	selector: "pacto-configuracao-ranking-professor",
	templateUrl: "./configuracao-ranking-professor.component.html",
	styleUrls: ["./configuracao-ranking-professor.component.scss"],
})
export class ConfiguracaoRankingProfessorComponent implements OnInit {
	@Input() title: string;
	@Input() resultado: any;
	@Input() acao: string;

	@ViewChild("addSuccess", { static: true }) addSuccess;
	@ViewChild("editSuccess", { static: true }) editSuccess;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("camposObrigatorios", { static: true }) camposObrigatorios;
	@ViewChild("campoNumerico", { static: true }) campoNumerico;
	@ViewChild("inputPeso", { static: true }) inputPeso;

	indicador: ColunasRankingProfessor = new ColunasRankingProfessor();
	@Input() indicadoresAdicionados: Array<any> = [];
	indicadoresOpcoes: Array<any> = [
		{ id: "PERCENTUAL_CRESCIMENTO_CARTEIRA", nome: "porcentagemCrescimento" },
		{ id: "ATIVOS", nome: "alunosAtivos" },
		{ id: "BI_TEMPO_CARTEIRA", nome: "mediaCarteira" },
		{ id: "PERCENTUAL_RENOVACAO_CARTEIRA", nome: "percRenovaramContrato" },
		{ id: "PERC_TREINO_EM_DIA", nome: "treinoEmDia" },
		{ id: "PERC_TREINO_VENCIDOS", nome: "treinoVencido" },
		{ id: "AG_CONFIRMACAO", nome: "agAguardandoConf" },
		{ id: "CANCELARAM", nome: "agCancelaram" },
		{ id: "CONFIRMARAM", nome: "agConfirmaram" },
		{ id: "FALTARAM", nome: "agFaltaram" },
		{ id: "AVALIACOES_FISICAS", nome: "agAvaliacoesRealizadas" },
		{ id: "COMPARECERAM", nome: "agCompareceram" },
		{ id: "NOVOS_TREINOS", nome: "agNovosTreino" },
		{ id: "OCUPACAO", nome: "agOcupacao" },
		{ id: "TREINOS_RENOVADOS", nome: "agTreinosRenovado" },
		{ id: "TREINOS_REVISADOS", nome: "agTreinosRevisado" },
		{ id: "AGENDAMENTOS", nome: "agendamentos" },
		{ id: "ALUNOS_APP_NAO_INSTALADO", nome: "alunosNaoUtilizaApp" },
		{ id: "ALUNOS_APP_INSTALADO_ATIVOS", nome: "alunosAtivosUtilizaApp" },
		{ id: "ALUNOS_CANCELADOS", nome: "alunosCancelados" },
		{ id: "INATIVOS", nome: "alunosInativos" },
		{ id: "ALUNOS_APP_INSTALADO", nome: "alunosInativosUtilizaApp" },
		{ id: "NAO_RENOVARAM", nome: "alunosNaoRenovaram" },
		{ id: "AVALIACOES", nome: "avaliacaoPeloApp" },
		{ id: "COM_AVALIACAO_FISICA", nome: "comAvaliacaoFisica" },
		{ id: "ATIVOS_COM_TREINO", nome: "comTreino" },
		{ id: "ALUNOS_A_VENCER", nome: "contratoAVencer" },
		{ id: "VENCIDOS", nome: "contratoVencido" },
		{ id: "AGENDAMENTOS_DISPONIBILIDADE", nome: "agDisponibilidade" },
		{ id: "NOVOS_CARTEIRA", nome: "entraramCarteira" },
		{ id: "HRS_ATENDIMENTO", nome: "agHorasAtendimento" },
		{ id: "HRS_DISPONIBILIDADE", nome: "agHorasDisponibilidade" },
		{ id: "BI_TEMPO_PROGRAMA", nome: "mediaPrograma" },
		{ id: "NOVOS_CARTEIRA_NOVOS", nome: "novosAlunos" },
		{ id: "ACESSOS", nome: "acessaramTreino" },
		{ id: "ACESSOS_TREINO", nome: "alunosDoTreinoAcessaramTreino" },
		{ id: "EXECUCOES_TREINO", nome: "execucoesTreino" },
		{ id: "SMARTPHONE", nome: "execucoesTreinoPeloApp" },
		{ id: "EM_DIA", nome: "programaEmDia" },
		{ id: "REAGENDARAM", nome: "agReagendaram" },
		{ id: "RENOVARAM", nome: "renovaramContrato" },
		{ id: "TROCARAM_CARTEIRA", nome: "sairamCarteira" },
		{ id: "SEM_AVALIACAO", nome: "semAvaliacaoFisica" },
		{ id: "ATIVOS_SEM_TREINO", nome: "alunosAtivosSemTreino" },
		{ id: "TOTAL_ALUNOS", nome: "totalAlunos" },
		{ id: "TREINOS_A_VENCER", nome: "treinosVencendo" },
		{ id: "NOVOS_CARTEIRA_TROCARAM", nome: "trocaramDeCarteira" },
		{ id: "DISPONIBILIDADES", nome: "disponibilidades" },
		{ id: "ACOMPANHADOS", nome: "acompanhados" },
		{ id: "ATIVIDADES_ACOMPANHADAS", nome: "atividadesAcompanhadas" },
	];
	formGroup: FormGroup = new FormGroup({
		indicador: new FormControl("", [Validators.required]),
		operacao: new FormControl("", [Validators.required]),
		peso: new FormControl("", [Validators.required]),
	});

	constructor(
		private snotifyService: SnotifyService,
		private professorService: TreinoApiProfessorService,
		private openModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private configuracaoService: TreinoApiConfiguracaoRankingService
	) {}

	ngOnInit() {
		if (this.resultado) {
			this.formGroup.get("indicador").setValue(this.resultado.indicador);
			this.formGroup.get("operacao").setValue(this.resultado.operacao);
			this.formGroup.get("peso").setValue(this.resultado.peso);
		}
	}

	get indicadores(): Array<any> {
		const ind = [];
		this.indicadoresOpcoes.forEach((i) => {
			if (
				this.indicadoresAdicionados.length === 0 ||
				!this.indicadoresAdicionados.includes(i.id) ||
				(this.resultado &&
					this.resultado.indicador &&
					i.id === this.resultado.indicador)
			) {
				ind.push(i);
			}
		});
		return ind;
	}

	dismiss() {
		this.openModal.close();
	}

	submitHandler() {
		this.formGroup.get("indicador").markAsTouched();
		this.formGroup.get("operacao").markAsTouched();
		this.formGroup.get("peso").markAsTouched();
		if (this.validForm()) {
			this.configuracaoService
				.incluirConfiguracao(this.formGroup.getRawValue())
				.subscribe((result) => {
					if (this.resultado) {
						const editSuccess = this.editSuccess.nativeElement.innerHTML;
						this.snotifyService.success(editSuccess);
					} else {
						const addSuccess = this.addSuccess.nativeElement.innerHTML;
						this.snotifyService.success(addSuccess);
					}
					this.openModal.close();
				});
		}
	}

	private validForm() {
		if (!this.formGroup.valid) {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(camposObrigatorios);
			return false;
		} else {
			return true;
		}
	}

	get showOperacaoErro() {
		const control = this.formGroup.get("operacao");
		return !control.valid && control.touched;
	}

	get showIndicadorErro() {
		const control = this.formGroup.get("indicador");
		return !control.valid && control.touched;
	}

	removeHandler(configuracaoId, index: number) {
		this.configuracaoService
			.removerConfiguracao(configuracaoId)
			.subscribe(() => {
				const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
				this.snotifyService.success(removeSuccess);
			});
	}
}
