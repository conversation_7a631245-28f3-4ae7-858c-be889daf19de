import {
	ChangeDetector<PERSON>ef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { NgbDropdown, NgbModal } from "@ng-bootstrap/ng-bootstrap";

import {
	GridFilterConfig,
	GridFilterType,
	LoaderService,
	PactoDataGridConfig,
	RelatorioComponent,
	FilterComponent,
	PactoDataGridColumnConfig,
	DataFiltro,
} from "ui-kit";
import { Observable, zip } from "rxjs";
import { map } from "rxjs/operators";

import {
	Modalidade,
	TreinoApiColaboradorService,
	TreinoApiConfiguracaoRankingService,
	TreinoApiProfessorService,
	TreinoApiModalidadeService,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { ConfiguracaoRankingProfessorComponent } from "./configuracao-ranking-professor/configuracao-ranking-professor.component";
import { RestService } from "@base-core/rest/rest.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { SessionService } from "@base-core/client/session.service";
import { ListaConfiguracaoRankingComponent } from "./lista-configuracao-ranking/lista-configuracao-ranking.component";
import { PodiumComponent } from "../podium/podium.component";
import { DetalhesProfessorComponent } from "./detalhes-professor/detalhes-professor.component";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "pacto-ranking-professores",
	templateUrl: "./ranking-professores.component.html",
	styleUrls: ["./ranking-professores.component.scss"],
})
export class RankingProfessoresComponent implements OnInit {
	// TABLE LABEL
	@ViewChild("tableTitulo", { static: true }) tableTitulo;
	@ViewChild("nomeTitulo", { static: true }) nomeTitulo;
	@ViewChild("totalAlunosTitulo", { static: true }) totalAlunosTitulo;
	@ViewChild("treinosVencendoTitulo", { static: true }) treinosVencendoTitulo;
	@ViewChild("trocaramDeCarteiraTitulo", { static: true })
	trocaramDeCarteiraTitulo;
	@ViewChild("porcentagemCrescimentoTitulo", { static: true })
	porcentagemCrescimentoTitulo;
	@ViewChild("percRenovaramContratoTitulo", { static: true })
	percRenovaramContratoTitulo;
	@ViewChild("treinoEmDiaTitulo", { static: true }) treinoEmDiaTitulo;
	@ViewChild("treinoVencidoTitulo", { static: true }) treinoVencidoTitulo;
	@ViewChild("agAguardandoConfTitulo", { static: true }) agAguardandoConfTitulo;
	@ViewChild("agCancelaramTitulo", { static: true }) agCancelaramTitulo;
	@ViewChild("agConfirmaramTitulo", { static: true }) agConfirmaramTitulo;
	@ViewChild("agFaltaramTitulo", { static: true }) agFaltaramTitulo;
	@ViewChild("agReagendaramTitulo", { static: true }) agReagendaramTitulo;
	@ViewChild("agAvaliacoesRealizadasTitulo", { static: true })
	agAvaliacoesRealizadasTitulo;
	@ViewChild("agCompareceramTitulo", { static: true }) agCompareceramTitulo;
	@ViewChild("agNovosTreinoTitulo", { static: true }) agNovosTreinoTitulo;
	@ViewChild("agOcupacaoTitulo", { static: true }) agOcupacaoTitulo;
	@ViewChild("agTreinosRenovadoTitulo", { static: true })
	agTreinosRenovadoTitulo;
	@ViewChild("agTreinosRevisadoTitulo", { static: true })
	agTreinosRevisadoTitulo;
	@ViewChild("agendamentosTitulo", { static: true }) agendamentosTitulo;
	@ViewChild("alunosAtivosTitulo", { static: true }) alunosAtivosTitulo;
	@ViewChild("alunosNaoUtilizaAppTitulo", { static: true })
	alunosNaoUtilizaAppTitulo;
	@ViewChild("alunosAtivosUtilizaAppTitulo", { static: true })
	alunosAtivosUtilizaAppTitulo;
	@ViewChild("alunosCanceladosTitulo", { static: true }) alunosCanceladosTitulo;
	@ViewChild("alunosInativosTitulo", { static: true }) alunosInativosTitulo;
	@ViewChild("alunosInativosUtilizaAppTitulo", { static: true })
	alunosInativosUtilizaAppTitulo;
	@ViewChild("alunosNaoRenovaramTitulo", { static: true })
	alunosNaoRenovaramTitulo;
	@ViewChild("estrela1Titulo", { static: true }) estrela1Titulo;
	@ViewChild("estrela2Titulo", { static: true }) estrela2Titulo;
	@ViewChild("estrela3Titulo", { static: true }) estrela3Titulo;
	@ViewChild("estrela4Titulo", { static: true }) estrela4Titulo;
	@ViewChild("estrela5Titulo", { static: true }) estrela5Titulo;
	@ViewChild("avaliacaoPeloAppTitulo", { static: true }) avaliacaoPeloAppTitulo;
	@ViewChild("comAvaliacaoFisicaTitulo", { static: true })
	comAvaliacaoFisicaTitulo;
	@ViewChild("comTreinoTitulo", { static: true }) comTreinoTitulo;
	@ViewChild("contratoAVencerTitulo", { static: true }) contratoAVencerTitulo;
	@ViewChild("contratoVencidoTitulo", { static: true }) contratoVencidoTitulo;
	@ViewChild("agDisponibilidadeTitulo", { static: true })
	agDisponibilidadeTitulo;
	@ViewChild("entraramCarteiraTitulo", { static: true }) entraramCarteiraTitulo;
	@ViewChild("agHorasAtendimentoTitulo", { static: true })
	agHorasAtendimentoTitulo;
	@ViewChild("agHorasDisponibilidadeTitulo", { static: true })
	agHorasDisponibilidadeTitulo;
	@ViewChild("mediaProgramaTitulo", { static: true }) mediaProgramaTitulo;
	@ViewChild("mediaCarteiraTitulo", { static: true }) mediaCarteiraTitulo;
	@ViewChild("novosAlunosTitulo", { static: true }) novosAlunosTitulo;
	@ViewChild("alunosDoTreinoAcessaramTreinoTitulo", { static: true })
	alunosDoTreinoAcessaramTreinoTitulo;
	@ViewChild("acessaramTreinoTitulo", { static: true }) acessaramTreinoTitulo;
	@ViewChild("execucoesTreinoTitulo", { static: true }) execucoesTreinoTitulo;
	@ViewChild("execucoesTreinoPeloAppTitulo", { static: true })
	execucoesTreinoPeloAppTitulo;
	@ViewChild("programaEmDiaTitulo", { static: true }) programaEmDiaTitulo;
	@ViewChild("renovaramContratoTitulo", { static: true })
	renovaramContratoTitulo;
	@ViewChild("sairamCarteiraTitulo", { static: true }) sairamCarteiraTitulo;
	@ViewChild("semAvaliacaoFisicaTitulo", { static: true })
	semAvaliacaoFisicaTitulo;
	@ViewChild("alunosAtivosSemTreinoTitulo", { static: true })
	alunosAtivosSemTreinoTitulo;
	@ViewChild("totalTitulo", { static: true }) totalTitulo;
	@ViewChild("acompanhadosTitulo", { static: true }) acompanhadosTitulo;
	@ViewChild("atividadesAcompanhadasTitulo", { static: true })
	atividadesAcompanhadasTitulo;
	// FILTER LABELS
	@ViewChild("mesAnoLabel", { static: true }) mesAnoLabel;
	@ViewChild("dataFimLabel", { static: false }) dataFimLabel;
	@ViewChild("professorLabel", { static: true }) professorLabel;

	@ViewChild("relatorio", { static: false }) relatorio: RelatorioComponent;
	@ViewChild("configurarRanking", { static: true })
	configurarRanking: TemplateRef<any>;
	@ViewChild("totalColumn", { static: true }) totalColumn;
	@ViewChild("desempenho", { static: true }) desempenho;
	@ViewChild("nomeColumn", { static: true }) nomeColumn;
	@ViewChild("podium", { static: true }) podium: PodiumComponent;
	@ViewChild("cargaHorariaLabel", { static: true }) cargaHorariaLabel;
	@ViewChild("situacaoLabel", { static: true }) situacaoLabel;
	@ViewChild("personalLabel", { static: true }) personalLabel;
	@ViewChild("filterTranslator", { static: true }) filterTranslator;
	@ViewChild("modalidadeLabel", { static: true }) modalidadeLabel;
	@ViewChild("dropDownPeriodo", { static: true, read: NgbDropdown })
	dropDownPeriodo: NgbDropdown;
	@ViewChild("filtro", { static: false })
	filtro: FilterComponent;
	baseFilter: DataFiltro = {};
	lastUpdate;
	filterOptions: {
		configs: {};
		filters: {};
	};
	formGroup: FormGroup = new FormGroup({
		dataInicio: new FormControl(new Date().setDate(1)),
		dataFim: new FormControl(new Date().getTime()),
	});
	permissaoAcessarConfiguracaoRanking = false;

	constructor(
		private modal: NgbModal,
		private rest: RestService,
		private configuracaoService: TreinoApiConfiguracaoRankingService,
		private colaboradorService: TreinoApiColaboradorService,
		private cd: ChangeDetectorRef,
		private professorService: TreinoApiProfessorService,
		private sessionService: SessionService,
		private modalidadeService: TreinoApiModalidadeService,
		private loaderService: LoaderService
	) {}

	private mes: any;
	private ano: any;
	private filtroData;
	table: PactoDataGridConfig;
	config: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	ready = false;
	modalConfig = ConfiguracaoRankingProfessorComponent;
	resultado: any;
	dados: Array<any> = [];
	indicadoresAdicionados: Array<any> = [];

	ngOnInit() {
		zip(this.getModalidade()).subscribe(() => {
			setTimeout(() => {
				this.professorService
					.conferirDatas(this.inicio, this.fim)
					.subscribe((s) => {
						this.configTable();
						this.ready = true;
						this.cd.detectChanges();
					});
			}, 500);
			this.configFilters();
		});
		this.sessionService.notificarRecursoEmpresa(
			RecursoSistema.RANKING_PROFESSORES
		);
		this.permissaoAcessarConfiguracaoRanking =
			this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.CONFIGURACOES_DO_RANKING
			);
		console.log(
			"permissaoAcessarConfiguracaoRanking ",
			this.permissaoAcessarConfiguracaoRanking
		);
	}

	getLastUpdate() {
		this.reload();
		this.lastUpdate =
			this.relatorio && this.relatorio.rawData && this.relatorio.rawData[0]
				? this.relatorio.rawData[0].geracao
				: null;
	}

	abrirDetalhes($event) {
		const urlDetalhes = this.rest.buildFullUrl(
			`professores/ranking/detalhes/compartilhar/${$event.colaborador}?inicio=${this.inicio}&fim=${this.fim}`
		);
		this.professorService
			.detalhesRankingProfessores($event.colaborador, this.inicio, this.fim)
			.subscribe((dados) => {
				const modalHandle = this.modal.open(DetalhesProfessorComponent, {
					windowClass: "modal-detalhes",
				});
				modalHandle.componentInstance.dadosRanking = $event;
				modalHandle.componentInstance.detalhes = dados.content;
				modalHandle.componentInstance.endpointUrl = urlDetalhes;
				modalHandle.result.then((result) => {});
			});
	}

	private tableInit() {
		this.configTable();
		this.configFilters();
	}

	reload() {
		this.podium.reload(this.filterOptions);
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		this.abrirDetalhes($event.row);
	}

	configurarRankingProfessores() {
		const modalHandle = this.modal.open(ListaConfiguracaoRankingComponent, {
			size: "lg",
		});
		modalHandle.result.then((result) => {
			if (result === "atualizar") {
				setTimeout(() => {
					this.atualizarHandler();
				}, 500);
			} else {
				this.tableInit();
				this.relatorio.reloadData();
			}
		});
	}

	atualizarHandler() {
		this.podium.clear();
		this.professorService
			.atualizarRankingProfessores(this.inicio, this.fim)
			.subscribe((dados) => {
				setTimeout(() => {
					this.tableInit();
					this.relatorio.reloadData();
					this.cd.detectChanges();
				}, 300);
			});
	}

	atualizar() {
		this.podium.clear();
		this.dropDownPeriodo.close();
		this.professorService
			.conferirDatas(this.inicio, this.fim)
			.subscribe((dados) => {
				setTimeout(() => {
					this.relatorio.fetchFiltros().filters.dataInicio = this.inicio;
					this.relatorio.fetchFiltros().filters.dataFim = this.fim;
					this.tableInit();
					this.relatorio.reloadData();
					this.cd.detectChanges();
				}, 300);
			});
	}

	get inicio() {
		return this.formGroup.get("dataInicio").value;
	}

	get fim() {
		return this.formGroup.get("dataFim").value;
	}

	private configTable() {
		const colunas = this.obterColunasRelatorio();
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(
				`professores/ranking?inicio=${this.inicio}&fim=${this.fim}`
			),
			exportButton: true,
			pagination: true,
			rowClick: false,
			showFilters: true,
			quickSearch: true,
			columns: colunas,
			actions: [
				{
					nome: "Detalhes",
					iconClass: "pct pct-zoom-in cor-azulim05",
					tooltipText: "Visualize mais informações do professor",
					actionFn: (row) => this.openModal(row),
				},
			],
		});
	}

	obterColunasRelatorio() {
		const colunas = [];
		const posicao: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "posicao",
			campo: "posicao",
			titulo: "Posição",
			ordenavel: true,
			defaultVisible: true,
		};
		const nome: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "nome",
			campo: "nome",
			titulo: "Professor",
			ordenavel: true,
			defaultVisible: true,
			celula: this.nomeColumn,
		};
		const desempenho: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "desempenho",
			campo: "desempenho",
			titulo: "Desempenho",
			ordenavel: false,
			defaultVisible: true,
			celula: this.desempenho,
		};
		const total: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "total",
			campo: "total",
			titulo: this.totalTitulo,
			ordenavel: true,
			defaultVisible: true,
			celula: this.totalColumn,
		};
		colunas.push(posicao);
		colunas.push(nome);
		colunas.push(desempenho);
		colunas.push(total);
		return colunas;
	}

	getFiltersShare(): DataFiltro {
		this.baseFilter.filters = {};
		this.baseFilter.configs = {};
		return this.baseFilter;
	}

	openModal(row) {}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "situacoes",
					type: GridFilterType.MANY,
					label: this.situacaoLabel,
					options: [
						{ value: "ativo", label: "Ativo" },
						{ value: "inativo", label: "Inativo" },
					],
					translator: this.filterTranslator,
					initialValue: ["ativo"],
				},
				{
					name: "cargaHoraria",
					type: GridFilterType.MANY,
					label: this.cargaHorariaLabel,
					options: [
						{ value: "2", label: "2 horas" },
						{ value: "4", label: "4 horas" },
						{ value: "6", label: "6 horas" },
						{ value: "8", label: "8 horas" },
						{ value: "12", label: "12 horas" },
					],
					translator: this.filterTranslator,
				},
				{
					name: "personal",
					type: GridFilterType.MANY,
					label: this.personalLabel,
					options: [
						{ value: "naoincluir", label: "Não incluir personal" },
						{ value: "interno", label: "Interno" },
						{ value: "externo", label: "Externo" },
					],
					translator: this.filterTranslator,
					initialValue: ["naoincluir"],
				},
				{
					name: "modalidadeIds",
					label: this.modalidadeLabel,
					type: GridFilterType.MANY,
					options: this.dados,
				},
			],
		};
	}

	private getModalidade(): Observable<any> {
		return this.modalidadeService.obterTodasModalidades().pipe(
			map((dados) => {
				dados.content.forEach((modalidade: any) => {
					this.dados.push({ value: modalidade.id, label: modalidade.nome });
				});
				return true;
			})
		);
	}

	onSearch(filtro) {
		this.filterOptions = filtro;
		const filter = this.relatorio.fetchFiltros();
		filter.filters.cargaHoraria = filtro.filters.cargaHoraria;
		filter.filters.situacoes = filtro.filters.situacoes;
		filter.filters.personal = filtro.filters.personal;
		filter.filters.modalidadeIds = filtro.filters.modalidadeIds;
		filter.filters.share = "true";
		this.podium.clear();
		this.filtro.close();
		this.relatorio.reloadData();
	}
}
