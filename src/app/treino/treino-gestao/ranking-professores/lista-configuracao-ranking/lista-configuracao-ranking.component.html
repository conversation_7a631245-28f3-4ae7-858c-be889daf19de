<div>
	<div class="modal-header">
		<h4 class="modal-title">
			<span i18n="@@relatorio-ranking-professores:configuracoes:titulo">
				Configurações
			</span>
		</h4>
		<button
			(click)="dismiss()"
			aria-label="Close"
			class="close modal-item"
			type="button">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div class="modal-body">
		<div class="search">
			<div>
				<input
					#quickSearch
					[formControl]="quickSearchControl"
					class="form-control"
					id="input-busca-rapida"
					placeholder="Busca rápida..."
					type="text" />
				<i class="pct pct-search"></i>
			</div>
		</div>
		<div [ngClass]="selecionado" class="tabs">
			<div (click)="selecionarAgrupamento('TREINO')" class="tab TREINO">
				Treino
			</div>
			<div (click)="selecionarAgrupamento('AGENDA')" class="tab AGENDA">
				Agenda
			</div>
			<div (click)="selecionarAgrupamento('ALUNOS')" class="tab ALUNOS">
				Alunos
			</div>
		</div>

		<pacto-relatorio
			#tableData
			[actionTitulo]="'Ações'"
			[enableZebraStyle]="true"
			[itensPerPage]="itensPerPage"
			[showShare]="false"
			[table]="table"></pacto-relatorio>

		<ng-template #indicadorColumn let-item="item">
			<ng-container
				*ngTemplateOutlet="
					traducaoItems;
					context: { traducaoItem: indicador[item.indicador] }
				"></ng-container>
		</ng-template>
		<ng-template #operacao let-item="item">
			<span *ngIf="item?.operacao === true">Adicionar</span>
			<span *ngIf="item?.operacao === false">Subtrair</span>
		</ng-template>
		<ng-template #pesoColumn let-item="item">
			<input
				[formControl]="formControlItem(item)"
				[textMask]="maskPontuacao"
				class="pontuacao"
				type="text" />
		</ng-template>

		<ng-template #ativoColumn let-item="item">
			<i
				(click)="toggleAtivo(item)"
				*ngIf="item.ativa === false"
				class="pct pct-minus-square"
				title="Indicador inativo"></i>
			<i
				(click)="toggleAtivo(item)"
				*ngIf="item.ativa === true"
				class="cfg-ranking pct pct-check-square"
				title="Indicador ativo"></i>
		</ng-template>
	</div>
</div>

<span
	#removeSuccess
	[hidden]="true"
	i18n="@@relatorio-ranking-professores:removido-success:mensagem">
	Configuração removida com sucesso.
</span>

<ng-template #traducaoItems let-traducaoItem="traducaoItem">
	<ng-container [ngSwitch]="traducaoItem">
		<span
			*ngSwitchCase="'totalAlunos'"
			i18n="@@relatorio-ranking-professores:total-alunos:indicador">
			Total de Alunos
		</span>
		<span
			*ngSwitchCase="'treinosVencendo'"
			i18n="@@relatorio-ranking-professores:treinos-vencendo:indicador">
			Treinos vencendo
		</span>
		<span
			*ngSwitchCase="'trocaramDeCarteira'"
			i18n="@@relatorio-ranking-professores:trocaram-carteira:indicador">
			Trocaram de carteira
		</span>
		<span
			*ngSwitchCase="'porcentagemCrescimento'"
			i18n="
				@@relatorio-ranking-professores:perc-crescimento-carteira:indicador">
			% crescimento carteira
		</span>
		<span
			*ngSwitchCase="'percRenovaramContrato'"
			i18n="@@relatorio-ranking-professores:perc-renovacao-contrato:indicador">
			% renovação de contrato
		</span>
		<span
			*ngSwitchCase="'treinoEmDia'"
			i18n="@@relatorio-ranking-professores:perc-treinos-dia:indicador">
			% treinos em dia
		</span>
		<span
			*ngSwitchCase="'treinoVencido'"
			i18n="@@relatorio-ranking-professores:perc-treinos-vencido:indicador">
			% treinos vencidos
		</span>
		<span
			*ngSwitchCase="'agAguardandoConf'"
			i18n="
				@@relatorio-ranking-professores:agenda-aguardando-confirmacao:indicador">
			Agenda: Aguardando confirmação
		</span>
		<span
			*ngSwitchCase="'agCancelaram'"
			i18n="@@relatorio-ranking-professores:agenda-alunos-cancelaram:indicador">
			Agenda: Alunos que cancelaram
		</span>
		<span
			*ngSwitchCase="'agConfirmaram'"
			i18n="
				@@relatorio-ranking-professores:agenda-alunos-conf-presenca:indicador">
			Agenda: Alunos que confirmaram presença
		</span>
		<span
			*ngSwitchCase="'agFaltaram'"
			i18n="@@relatorio-ranking-professores:agenda-alunos-faltaram:indicador">
			Agenda: Alunos que faltaram
		</span>
		<span
			*ngSwitchCase="'agReagendaram'"
			i18n="
				@@relatorio-ranking-professores:agenda-alunos-reagendaram:indicador">
			Agenda: Alunos que reagendaram
		</span>
		<span
			*ngSwitchCase="'agAvaliacoesRealizadas'"
			i18n="
				@@relatorio-ranking-professores:agenda-avaliacoes-fisicas-realizadas:indicador">
			Agenda: Av. físicas que foram realizadas
		</span>
		<span
			*ngSwitchCase="'agCompareceram'"
			i18n="@@relatorio-ranking-professores:agenda-compareceram:indicador">
			Agenda: Compareceram
		</span>
		<span
			*ngSwitchCase="'agNovosTreino'"
			i18n="@@relatorio-ranking-professores:agenda-novos-treinos:indicador">
			Agenda: Novos treinos
		</span>
		<span
			*ngSwitchCase="'agOcupacao'"
			i18n="@@relatorio-ranking-professores:agenda-ocupacao:indicador">
			Agenda: Ocupação
		</span>
		<span
			*ngSwitchCase="'agTreinosRenovado'"
			i18n="@@relatorio-ranking-professores:agenda-treinos-renovados:indicador">
			Agenda: Treinos que foram renovados
		</span>
		<span
			*ngSwitchCase="'agTreinosRevisado'"
			i18n="@@relatorio-ranking-professores:agenda-treinos-revisados:indicador">
			Agenda: Treinos que foram revisados
		</span>
		<span
			*ngSwitchCase="'agendamentos'"
			i18n="@@relatorio-ranking-professores:agendamentos:indicador">
			Agendamentos
		</span>
		<span
			*ngSwitchCase="'alunosAtivos'"
			i18n="@@relatorio-ranking-professores:alunos-ativos:indicador">
			Alunos Ativos
		</span>
		<span
			*ngSwitchCase="'alunosNaoUtilizaApp'"
			i18n="
				@@relatorio-ranking-professores:alunos-ativos-nao-usa-app:indicador">
			Alunos ativos que não utilizam o App
		</span>
		<span
			*ngSwitchCase="'alunosAtivosUtilizaApp'"
			i18n="@@relatorio-ranking-professores:alunos-ativos-usa-app:indicador">
			Alunos ativos que utilizam o App
		</span>
		<span
			*ngSwitchCase="'alunosCancelados'"
			i18n="@@relatorio-ranking-professores:alunos-cancelados:indicador">
			Alunos cancelados
		</span>
		<span
			*ngSwitchCase="'alunosInativos'"
			i18n="@@relatorio-ranking-professores:alunos-inativos:indicador">
			Alunos Inativos
		</span>
		<span
			*ngSwitchCase="'alunosInativosUtilizaApp'"
			i18n="@@relatorio-ranking-professores:alunos-inativos-usa-app:indicador">
			Alunos inativos que utilizam o App
		</span>
		<span
			*ngSwitchCase="'alunosNaoRenovaram'"
			i18n="
				@@relatorio-ranking-professores:alunos-nao-renovaram-contrato:indicador">
			Alunos que não renovaram contrato
		</span>
		<span
			*ngSwitchCase="'estrela1'"
			i18n="@@relatorio-ranking-professores:avaliacoes-1-estrela:indicador">
			Avaliações com 1 estrela
		</span>
		<span
			*ngSwitchCase="'estrela2'"
			i18n="@@relatorio-ranking-professores:avaliacoes-2-estrela:indicador">
			Avaliações com 2 estrela
		</span>
		<span
			*ngSwitchCase="'estrela3'"
			i18n="@@relatorio-ranking-professores:avaliacoes-3-estrela:indicador">
			Avaliações com 3 estrela
		</span>
		<span
			*ngSwitchCase="'estrela4'"
			i18n="@@relatorio-ranking-professores:avaliacoes-4-estrela:indicador">
			Avaliações com 4 estrela
		</span>
		<span
			*ngSwitchCase="'estrela5'"
			i18n="@@relatorio-ranking-professores:avaliacoes-5-estrela:indicador">
			Avaliações com 5 estrela
		</span>
		<span
			*ngSwitchCase="'avaliacaoPeloApp'"
			i18n="@@relatorio-ranking-professores:avaliacoes-pelo-app:indicador">
			Avaliações pelo aplicativo
		</span>
		<span
			*ngSwitchCase="'comAvaliacaoFisica'"
			i18n="@@relatorio-ranking-professores:com-avaliacao-fisica:indicador">
			Com Avaliação Física
		</span>
		<span
			*ngSwitchCase="'comTreino'"
			i18n="@@relatorio-ranking-professores:com-treino:indicador">
			Com treino
		</span>
		<span
			*ngSwitchCase="'contratoAVencer'"
			i18n="@@relatorio-ranking-professores:contratos-a-vencer:indicador">
			Contratos a vencer
		</span>
		<span
			*ngSwitchCase="'contratoVencido'"
			i18n="@@relatorio-ranking-professores:contratos-vencidos:indicador">
			Contratos vencidos
		</span>
		<span
			*ngSwitchCase="'agDisponibilidade'"
			i18n="@@relatorio-ranking-professores:disponibilidade:indicador">
			Disponibilidade
		</span>
		<span
			*ngSwitchCase="'entraramCarteira'"
			i18n="@@relatorio-ranking-professores:entraram-na-carteira:indicador">
			Entraram na carteira
		</span>
		<span
			*ngSwitchCase="'agHorasAtendimento'"
			i18n="@@relatorio-ranking-professores:horas-atendimento-agenda:indicador">
			Horas atendimento na agenda
		</span>
		<span
			*ngSwitchCase="'agHorasDisponibilidade'"
			i18n="
				@@relatorio-ranking-professores:horas-disponibilidade-agenda:indicador">
			Horas disponibilidade na agenda
		</span>
		<span
			*ngSwitchCase="'mediaPrograma'"
			i18n="@@relatorio-ranking-professores:media-programa-treino:indicador">
			Média do programa de treino (meses)
		</span>
		<span
			*ngSwitchCase="'mediaCarteira'"
			i18n="@@relatorio-ranking-professores:media-carteira:indicador">
			Média na carteira (meses)
		</span>
		<span
			*ngSwitchCase="'novosAlunos'"
			i18n="@@relatorio-ranking-professores:novos-alunos-carteira:indicador">
			Novos alunos na carteira
		</span>
		<span
			*ngSwitchCase="'alunosDoTreinoAcessaramTreino'"
			i18n="
				@@relatorio-ranking-professores:numero-alunos-acessaram-treino:indicador">
			Nr. alunos do Treino que acessaram
		</span>
		<span
			*ngSwitchCase="'acessaramTreino'"
			i18n="@@relatorio-ranking-professores:numero-alunos-acessaram:indicador">
			Nr. alunos que acessaram
		</span>
		<span
			*ngSwitchCase="'execucoesTreino'"
			i18n="@@relatorio-ranking-professores:numero-execucoes-treino:indicador">
			Nr. de execuções de Treino
		</span>
		<span
			*ngSwitchCase="'execucoesTreinoPeloApp'"
			i18n="
				@@relatorio-ranking-professores:numero-execucoes-treino-app:indicador">
			Nr. de execuções de Treino pelo aplicativo
		</span>
		<span
			*ngSwitchCase="'programaEmDia'"
			i18n="@@relatorio-ranking-professores:programa-em-dia:indicador">
			Programa em dia
		</span>
		<span
			*ngSwitchCase="'renovaramContrato'"
			i18n="@@relatorio-ranking-professores:renovaram-contrato:indicador">
			Renovaram contrato
		</span>
		<span
			*ngSwitchCase="'sairamCarteira'"
			i18n="@@relatorio-ranking-professores:sairam-carteira:indicador">
			Saíram da carteira
		</span>
		<span
			*ngSwitchCase="'semAvaliacaoFisica'"
			i18n="@@relatorio-ranking-professores:sem-avaliacao-fisica:indicador">
			Sem Avaliação Física
		</span>
		<span
			*ngSwitchCase="'alunosAtivosSemTreino'"
			i18n="@@relatorio-ranking-professores:sem-treino:indicador">
			Sem treino
		</span>
		<span
			*ngSwitchCase="'disponibilidades'"
			i18n="@@relatorio-ranking-professores:disponibilidades:indicador">
			Disponibilidades
		</span>
		<span
			*ngSwitchCase="'acompanhados'"
			i18n="@@relatorio-ranking-professores:acompanhados:indicador">
			Acompanhados
		</span>
		<span
			*ngSwitchCase="'atividadesAcompanhadas'"
			i18n="@@relatorio-ranking-professores:atividades-acompanhadas:indicador">
			Atividades Acompanhadas
		</span>
	</ng-container>
</ng-template>
