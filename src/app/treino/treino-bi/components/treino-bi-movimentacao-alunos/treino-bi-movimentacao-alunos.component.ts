import {
	ChangeDetectionStrategy,
	ChangeDetector<PERSON><PERSON>,
	<PERSON>mponent,
	<PERSON><PERSON><PERSON><PERSON>,
	OnInit,
} from "@angular/core";
import { TreinoBiStateService } from "../treino-bi-home-v2/treino-bi-state.service";
import { MediaExecucaoPorDia } from "@treino-core/treino-bi/treino-bi2.model";
import {
	ColumnChartSet,
	PactoColor,
	PactoDataGridConfig,
	PactoDataGridConfigDto,
	RelatorioComponent,
} from "ui-kit";
import { Subscription } from "rxjs";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";
import { TreinoApiBiService } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { DetalhesModalComponent } from "./detalhes-modal/detalhes-modal.component";
import { HttpClient } from "@angular/common/http";

enum PeriodoDia {
	MANHA = "MANHA",
	TARDE = "TARDE",
	NOITE = "NOITE",
}

enum DiaSemana {
	SEGUNDA = "segunda",
	TERCA = "terca",
	QUARTA = "quarta",
	QUINTA = "quinta",
	SEXTA = "sexta",
	SABADO = "sabado",
	DOMINGO = "domingo",
}

interface PeriodoMovimento {
	execucoes: number;
	periodo: PeriodoDia;
	diaSemana: DiaSemana;
}

@Component({
	selector: "pacto-treino-bi-movimentacao-alunos",
	templateUrl: "./treino-bi-movimentacao-alunos.component.html",
	styleUrls: ["./treino-bi-movimentacao-alunos.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TreinoBiMovimentacaoAlunosComponent implements OnInit, OnDestroy {
	table: PactoDataGridConfig;
	relatorio: RelatorioComponent;
	config: PactoDataGridConfigDto;

	constructor(
		private modalService: NgbModal,
		private rest: RestService,
		public sessionService: SessionService,
		private treinoBiService: TreinoApiBiService,
		private cd: ChangeDetectorRef,
		private biState: TreinoBiStateService,
		private configService: TreinoConfigCacheService,
		private http: HttpClient
	) {}

	subscription: Subscription;

	series: ColumnChartSet[];

	maiorPeriodo: PeriodoMovimento;
	menorPeriodo: PeriodoMovimento;
	chartColors: PactoColor[] = [
		PactoColor.PEQUIZAO_PRI,
		PactoColor.AZULIM_PRI,
		PactoColor.AZUL_PACTO_PRI,
	];
	configuracaoGestao;

	ngOnInit() {
		this.configuracaoGestao =
			this.configService.configuracoesGestao.periodo_usado_bi;
		this.subscription = this.biState.update$.subscribe((ready) => {
			if (ready) {
				this.setupData();
				setTimeout(() => {
					this.cd.detectChanges();
				});
			}
		});
	}

	ngOnDestroy(): void {
		this.subscription.unsubscribe();
	}

	get PactoColor() {
		return PactoColor;
	}

	private async setupData() {
		await this.setupChartSeries();
		const execucoes = this.biState.treinamento.mediaExecucao;
		this.maiorPeriodo = this.obterPeriodoMaiorMenor(execucoes);
		this.menorPeriodo = this.obterPeriodoMaiorMenor(execucoes, false);
	}

	private async setupChartSeries() {
		try {
			const url = this.rest.buildFullUrl(
				`treino-bi/resumo-execucoes-periodo/${this.sessionService.empresaId}`
			);
			const response = await this.http.get<any>(url).toPromise();

			const resumo = response.content || response;

			const manha = [];
			const tarde = [];
			const noite = [];

			const diasOrdem = [
				"segunda",
				"terca",
				"quarta",
				"quinta",
				"sexta",
				"sabado",
				"domingo",
			];

			for (const dia of diasOrdem) {
				if (resumo[dia]) {
					const dadosDia = resumo[dia];
					manha.push(dadosDia.manha || 0);
					tarde.push(dadosDia.tarde || 0);
					noite.push(dadosDia.noite || 0);
				} else {
					manha.push(0);
					tarde.push(0);
					noite.push(0);
				}
			}

			this.series = [
				{ name: "Manhã", data: manha },
				{ name: "Tarde", data: tarde },
				{ name: "Noite", data: noite },
			];
			this.cd.detectChanges();
		} catch (error) {
			console.error("Erro ao obter resumo de execuções:", error);
			this.setupChartSeriesOriginal();
		}
	}

	private setupChartSeriesOriginal() {
		const media = this.biState.treinamento.mediaExecucao;
		const manha = [];
		const tarde = [];
		const noite = [];

		for (const diaSemana in media) {
			if (media.hasOwnProperty(diaSemana)) {
				manha.push(media[diaSemana].manha);
				tarde.push(media[diaSemana].tarde);
				noite.push(media[diaSemana].noite);
			}
		}
		this.series = [
			{ name: "Manhã", data: manha },
			{ name: "Tarde", data: tarde },
			{ name: "Noite", data: noite },
		];
	}

	private obterPeriodoMaiorMenor(
		execucao: MediaExecucaoPorDia,
		maior = true
	): PeriodoMovimento {
		let maiorPeriodo: PeriodoDia = null;
		let maiorDiaSemana: DiaSemana = null;
		let maiorNumeroExecucoes = null;

		for (const diaSemana in execucao) {
			if (execucao.hasOwnProperty(diaSemana)) {
				// Manhã
				const filtroManha = maior
					? execucao[diaSemana].manha > maiorNumeroExecucoes
					: execucao[diaSemana].manha < maiorNumeroExecucoes;
				if (
					(maiorNumeroExecucoes === null || filtroManha) &&
					execucao[diaSemana].manha > 0
				) {
					maiorNumeroExecucoes = execucao[diaSemana].manha;
					maiorPeriodo = PeriodoDia.MANHA;
					maiorDiaSemana = diaSemana as DiaSemana;
				}

				// Tarde
				const filtroTarde = maior
					? execucao[diaSemana].tarde > maiorNumeroExecucoes
					: execucao[diaSemana].tarde < maiorNumeroExecucoes;
				if (
					(maiorNumeroExecucoes === null || filtroTarde) &&
					execucao[diaSemana].tarde > 0
				) {
					maiorNumeroExecucoes = execucao[diaSemana].tarde;
					maiorPeriodo = PeriodoDia.TARDE;
					maiorDiaSemana = diaSemana as DiaSemana;
				}

				// Noite
				const filtroNoite = maior
					? execucao[diaSemana].noite > maiorNumeroExecucoes
					: execucao[diaSemana].noite < maiorNumeroExecucoes;
				if (
					(maiorNumeroExecucoes === null || filtroNoite) &&
					execucao[diaSemana].noite > 0
				) {
					maiorNumeroExecucoes = execucao[diaSemana].noite;
					maiorPeriodo = PeriodoDia.NOITE;
					maiorDiaSemana = diaSemana as DiaSemana;
				}
			}
		}

		return {
			periodo: maiorPeriodo,
			diaSemana: maiorDiaSemana,
			execucoes: maiorNumeroExecucoes,
		};
	}

	clickColumn(event) {
		if (!event || !event.opts) {
			console.error("Evento inválido:", event);
			return;
		}
		const mapeamentoDias = [2, 3, 4, 5, 6, 7, 1]; // [segunda=2, terca=3, ..., domingo=1]
		let dia = mapeamentoDias[event.opts.dataPointIndex];

		let periodo;
		if (
			event.opts.config &&
			event.opts.config.series &&
			event.opts.config.series[event.opts.seriesIndex]
		) {
			periodo = event.opts.config.series[event.opts.seriesIndex].name;
		} else if (event.opts.seriesIndex !== undefined) {
			// Fallback: mapear índice da série para período
			const periodos = ["Manhã", "Tarde", "Noite"];
			periodo = periodos[event.opts.seriesIndex];
		} else {
			console.error("Não foi possível determinar o período do clique");
			return;
		}

		let filter = this.buildfilters();
		let telaID;
		let telaTitle;
		if (periodo == "Manhã") {
			telaID = "execucoesTreinoUltimosDiasManha";
			telaTitle = "Execuções de treino nos últimos dias Manhã";
		} else if (periodo == "Tarde") {
			telaID = "execucoesTreinoUltimosDiasTarde";
			telaTitle = "Execuções de treino nos últimos dias Tarde";
		} else if (periodo == "Noite") {
			telaID = "execucoesTreinoUltimosDiasNoite";
			telaTitle = "Execuções de treino nos últimos dias Noite";
		}

		this.buildRelatorioAlunosAtivos(dia, periodo);
		const modal = this.modalService.open(DetalhesModalComponent, {
			size: "lg",
			windowClass: "modal-mxl",
			centered: true,
		});
		modal.componentInstance.telaId = telaID;
		modal.componentInstance.sessionService = this.sessionService;
		this.table.logUrl = this.rest.buildFullUrl(
			"log/listar-log-exportacao/" + telaID
		);
		modal.componentInstance.ready(this.table, telaTitle, filter, telaID);
	}

	private buildfilters() {
		const filter = {
			filters: {},
		};
		return filter;
	}

	buildRelatorioAlunosAtivos(dia, periodo) {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(
				"treino-bi/lista-alunos-execucao-treino-ultimos-dias/" +
					Number(this.sessionService.empresaId) +
					"/" +
					dia +
					"/" +
					periodo
			),
			quickSearch: true,
			rowClick: true,
			columns: [
				{
					nome: "matricula",
					titulo: "Matricula",
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "matricula",
					width: "200px",
				},
				{
					nome: "nome",
					titulo: "Nome",
					mostrarTitulo: true,
					buscaRapida: true,
					ordenavel: true,
					visible: true,
					defaultVisible: true,
					campo: "nome",
				},
			],
		});
	}
}
