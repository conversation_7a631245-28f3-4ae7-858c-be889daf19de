import { Component, Input, OnInit } from "@angular/core";
import { RestService } from "@base-core/rest/rest.service";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { PactoDataGridConfig } from "ui-kit";
import { DetalhesDoAcessoComponent } from "../detalhes-do-acesso/detalhes-do-acesso.component";

@Component({
	selector: "pacto-ver-acessos",
	templateUrl: "./ver-acessos.component.html",
	styleUrls: ["./ver-acessos.component.scss"],
})
export class VerAcessosComponent implements OnInit {
	public table: PactoDataGridConfig;

	@Input()
	public codigoPessoa: number;

	constructor(
		private readonly rest: RestService,
		private readonly ngbModal: NgbModal,
		private readonly activeModal: NgbActiveModal
	) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			rowClick: false,
			pagination: true,
			quickSearch: true,
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`acessos-cliente/by-pessoa/${this.codigoPessoa}`
			),
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					ordenavel: false,
					visible: true,
					valueTransform(v) {
						return v || "-";
					},
				},
				{
					nome: "sentido",
					titulo: "Sentido",
					ordenavel: false,
					visible: true,
					valueTransform(v) {
						switch (v) {
							case "E":
								return "Entrada";
							case "S":
								return "Saída";
							default:
								return "-";
						}
					},
				},
				{
					nome: "localAcesso",
					titulo: "Local",
					ordenavel: false,
					visible: true,
					valueTransform(v) {
						return v && v.descricao ? v.descricao : "-";
					},
				},
				{
					nome: "coletor",
					titulo: "Coletor",
					ordenavel: false,
					visible: true,
					valueTransform(v) {
						return v && v.descricao ? v.descricao : "-";
					},
				},
				{
					nome: "dtHrEntrada",
					titulo: "Entrada",
					ordenavel: true,
					visible: true,
					valueTransform(v) {
						return v ? new Date(v).toLocaleString() : "-";
					},
				},
				{
					nome: "dtHrSaida",
					titulo: "Saída",
					ordenavel: true,
					visible: true,
					valueTransform(v) {
						return v ? new Date(v).toLocaleString() : "-";
					},
				},
				{
					nome: "meioIdentificacaoEntrada",
					titulo: "Meio identificador",
					ordenavel: false,
					visible: true,
					valueTransform(v) {
						const meioIdentificacaoEntrada = {
							1: "Leitura de uma digital",
							2: "Digitado a matrícula no teclado do computador",
							3: "Digitado a matrícula do cliente no teclado da catraca",
							4: "Leitura de código de barras serial",
							5: "Digitado a senha de acesso na catraca",
							6: "Presença em Lista de Chamada",
							7: "Liberação de Acesso Rápido",
							8: "A Definir...",
							9: "Digital Catraca",
							10: "Digitado o código de barras no teclado do computador",
							11: "Inserido manualmente",
							12: "Reconhecimento Facial",
							13: "Aplicativo",
							14: "Retira Ficha",
							15: "Importação",
							16: "Acesso Fácil",
							17: "Sistema Pacto",
						};

						return meioIdentificacaoEntrada.hasOwnProperty(v)
							? meioIdentificacaoEntrada[v]
							: "-";
					},
				},
			],
			actions: [
				{
					nome: `verDetalhes`,
					iconClass: "pct pct-search cor-azulim05",
					tooltipText: `Visualizar detalhes`,
				},
			],
		});
	}

	public verDetalhes(event): void {
		const modal = this.ngbModal.open(DetalhesDoAcessoComponent, {
			centered: true,
			size: "lg",
		});
		modal.componentInstance.detalhes = event.row;
		modal.componentInstance.codigoPessoa = this.codigoPessoa;
		modal.componentInstance.verAcessosComponent = VerAcessosComponent;
		this.activeModal.close();
	}

	public async closeHandler() {
		this.activeModal.close();
	}
}
