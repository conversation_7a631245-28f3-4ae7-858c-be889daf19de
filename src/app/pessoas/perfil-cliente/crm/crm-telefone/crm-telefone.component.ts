import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { Contato, CrmApiGenericoService } from "crm-api";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";
import { ModalScriptComponent } from "../modals/modal-script/modal-script.component";
import { ModalObjecaoComponent } from "../modals/modal-objecao/modal-objecao.component";
import { ClienteDadosPessoais } from "adm-core-api";
import { SessionService } from "@base-core/client/session.service";
import { ModalCrmAgendarComponent } from "../modals/modal-crm-agendar/modal-crm-agendar.component";
import { AdmLegadoTelaClienteService } from "adm-legado-api";

@Component({
	selector: "pacto-crm-telefone",
	templateUrl: "./crm-telefone.component.html",
	styleUrls: ["./crm-telefone.component.scss"],
})
export class CrmTelefoneComponent implements OnInit {
	// "TE", "Contato Telefônico");
	// "WA", "Contato WhatsApp");
	// "EM", "Contato E-mail");
	// "PE", "Contato Pessoal");
	// "LC", "Ligação sem contato");
	// "CS", "Contato SMS");
	// "AP", "Contato APP");
	@Input() tipoContato: string;
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Output() reloadHist: EventEmitter<any> = new EventEmitter<any>();
	@Input() traducao: TraducoesXinglingComponent;
	form = new FormGroup({
		pesquisa: new FormControl(""),
		telefoneCelular: new FormControl(""),
		observacao: new FormControl("", [Validators.required]),
	});
	contato: Contato = {};
	matricula: string;

	constructor(
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private notify: SnotifyService,
		private rest: RestService,
		private router: Router,
		private modalService: DialogService,
		private crmService: CrmApiGenericoService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService
	) {}

	get _rest() {
		return this.rest;
	}

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.form.get("pesquisa").valueChanges.subscribe((value) => {
			if (!value) {
				return;
			}
			this.selecionouPesquisa();
		});
	}

	limparForm() {
		this.form = new FormGroup({
			pesquisa: new FormControl(""),
			telefoneCelular: new FormControl(""),
			observacao: new FormControl("", [Validators.required]),
		});
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	openScript() {
		const modal = this.modalService.open(
			"Script",
			ModalScriptComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.selectedContract = this.form.get("pesquisa");
		modal.componentInstance.dadosPessoais = this.dadosPessoais;
		modal.componentInstance.contato = this.getContato();
		modal.componentInstance.traducao = this.traducao;
		modal.componentInstance.response.subscribe((res) => {
			if (res === "reloadHist") {
				this.reloadHist.emit();
				this.limparForm();
				this.cd.detectChanges();
			}
		});
	}

	openModalObjecao() {
		if (this.validarDados()) {
			return;
		}
		const modal = this.modalService.open(
			"Objeção",
			ModalObjecaoComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.dadosPessoais = this.dadosPessoais;
		modal.componentInstance.contato = this.getContato();
		modal.componentInstance.traducao = this.traducao;
		modal.componentInstance.response.subscribe((res) => {
			if (res === "reloadHist") {
				this.reloadHist.emit();
				this.limparForm();
				this.cd.detectChanges();
			} else if (res === "reloadCliente") {
				this.reloadHist.emit();
				this.limparForm();
				this.cd.detectChanges();
				this.router.navigate(["pessoas", "perfil-v2", this.matricula, "crm"], {
					queryParams: { reload: new Date().getTime() },
				});
			}
		});
	}

	selecionouPesquisa() {
		const pesquisa = this.form.get("pesquisa").value;
		if (pesquisa && pesquisa.codigo && pesquisa.codigo > 0) {
			this.crmService
				.geraLinkPesquisa(
					pesquisa.codigo,
					this.dadosPessoais.codigoCliente,
					this.sessionService.codigoUsuarioZw
				)
				.subscribe(
					(ret) => {
						const obs = this.form.get("observacao").value;
						this.form.get("observacao").setValue(obs + ret[0].url);
					},
					(httpErrorResponse) => {
						if (
							httpErrorResponse.error &&
							httpErrorResponse.error.meta &&
							httpErrorResponse.error.meta.message
						) {
							this.notify.error(httpErrorResponse.error.meta.message);
						} else {
							this.notify.error(
								this.traducao.getLabel("falha-ao-tentar-salvar-contato")
							);
						}
					}
				);
		}
	}

	getContato() {
		this.contato.fase = "";
		this.contato.contatoavulso = true;
		this.contato.dia = new Date().getTime().toString();
		this.contato.cliente = this.dadosPessoais.codigoCliente;
		this.contato.responsavelcadastro = this.sessionService.codigoUsuarioZw;
		this.contato.tipocontato = this.tipoContato;
		this.contato.observacao = this.form.get("observacao").value;
		return this.contato;
	}

	formatarNumeroWhatsApp(
		ddiRaw: string | undefined,
		numeroRaw: string
	): string {
		if (!numeroRaw) {
			return "";
		}

		const ddi = (ddiRaw || "55").replace(/\D/g, "");
		let numero = numeroRaw.replace(/\D/g, "");

		numero = numero.replace(/^0+/, "");

		const tamanhoDdi = ddi.length;
		const prefixoNumero = numero.substring(0, tamanhoDdi);

		if (prefixoNumero === ddi) {
			numero = numero.substring(tamanhoDdi);
		}

		return ddi + numero;
	}

	simplesRegistro() {
		if (this.validarDados()) {
			return;
		}
		this.admLegadoTelaClienteService
			.salvarHistoricoContato(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				this.dadosPessoais.codigoCliente,
				this.getContato()
			)
			.subscribe(
				(resp) => {
					this.reloadHist.emit();
					this.notify.success("Contato salvo com sucesso.");
					if (
						this.isWhatsApp() &&
						this.form.get("telefoneCelular").value &&
						this.form.get("telefoneCelular").value.length > 0
					) {
						const telCelular = this.form.get("telefoneCelular").value;
						const observacao = this.form.get("observacao").value;
						const ddi =
							(this.dadosPessoais &&
								this.dadosPessoais.telefones &&
								this.dadosPessoais.telefones[0] &&
								this.dadosPessoais.telefones[0].ddi) ||
							"55";
						const numeroFormatado = this.formatarNumeroWhatsApp(
							ddi,
							telCelular
						);
						const target =
							"https://api.whatsapp.com/send?phone=" +
							numeroFormatado +
							"&text=" +
							encodeURIComponent(observacao);
						window.open(target, "_blank");
					}
					this.limparForm();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notify.error(err.meta.message);
						return;
					}
				}
			);
	}

	validarDados() {
		if (
			this.isWhatsApp() &&
			this.form.get("telefoneCelular").value.length <= 0
		) {
			this.notify.error("Selecione um número");
			return true;
		}
		if (!this.form.get("observacao").valid) {
			this.notify.error("Informe uma descrição");
			return true;
		}
		return false;
	}

	openModalAgendar(tipoAgendamento: string) {
		if (this.validarDados()) {
			return;
		}
		// tipoAgendamento
		// LI = Ligacao
		// AE = Aula Experimental
		// VI = Visita
		let titulo = "";
		if (tipoAgendamento === "LI") {
			titulo = "Agendar ligação";
		} else if (tipoAgendamento === "AE") {
			titulo = "Agendar aula experimental";
		} else if (tipoAgendamento === "VI") {
			titulo = "Agendar visita";
		}
		const modal = this.modalService.open(
			titulo,
			ModalCrmAgendarComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.tipoAgendamento = tipoAgendamento;
		modal.componentInstance.dadosPessoais = this.dadosPessoais;
		modal.componentInstance.contato = this.getContato();
		modal.componentInstance.traducao = this.traducao;
		modal.componentInstance.response.subscribe((res) => {
			if (res === "reloadHist") {
				this.reloadHist.emit();
				this.limparForm();
				this.cd.detectChanges();
			}
		});
	}

	isWhatsApp() {
		return this.tipoContato === "WA";
	}

	isTelefone() {
		return this.tipoContato === "TE";
	}

	isPessoal() {
		return this.tipoContato === "PE";
	}

	getTelefonesCelular() {
		const celulares = [];
		celulares.push({ id: "", label: "-" });

		for (const telefone of this.dadosPessoais.telefones) {
			if (telefone.whatsapp) {
				const ddi = telefone.ddi ? `+${telefone.ddi} ` : "";
				const numero = telefone.numero;
				const numeroCompleto = `${ddi}${numero}`;

				celulares.push({
					id: numeroCompleto,
					label: numeroCompleto,
				});
			}
		}
		return celulares;
	}
}
